package com.tydic.nbchat.train.report.promotion.bing.config;

import com.tydic.nbchat.train.report.promotion.bing.bo.ApplicationBO;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/27
 * @description Bing推广配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "nbchat-train.config.promotion.bing")
public class BingPromotionConfigProperties {
    
    /**
     * 用于报表查询的客户ID
     */
    private String reportDataCustomerId;
    
    /**
     * 应用配置列表
     */
    private List<ApplicationBO> appConfig;
    
    /**
     * 每页查询限制
     */
    private int limit = 200;

    /**
     * OAuth回调地址
     */
    private String redirectUri = "http://localhost:8705/train/callback/bing";
}
