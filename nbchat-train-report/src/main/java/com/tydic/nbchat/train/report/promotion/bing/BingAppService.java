package com.tydic.nbchat.train.report.promotion.bing;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.train.mapper.po.RpSearchLog;
import com.tydic.nbchat.train.report.promotion.AppService;
import com.tydic.nbchat.train.report.promotion.bing.bo.ApplicationBO;
import com.tydic.nbchat.train.report.promotion.bing.bo.BingCallbackBO;
import com.tydic.nbchat.train.report.promotion.bing.config.BingPromotionConfigProperties;
import com.tydic.nbchat.train.report.promotion.bo.AccountTokenBO;
import com.tydic.nbchat.train.report.promotion.bo.PromotionTypeEnum;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.http.RestApiHelper;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.ByteArrayInputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.tydic.nbchat.train.report.promotion.bo.AppServiceEnum.BING;
import static com.tydic.nbchat.train.report.promotion.bo.PromotionTypeEnum.ALL_PROMOTION_TYPE;
import static com.tydic.nicc.dc.boot.starter.util.DateTimeUtil.DATE_FORMAT_NORMAL;

/**
 * <AUTHOR>
 * @date 2025/6/26
 * @description Bing广告三方推广服务
 */
@Slf4j
@Service
public class BingAppService extends AppService {

    private static final String BING_API_BASE_URL = "https://reporting.api.bingads.microsoft.com";
    private static final String REPORT_SERVICE_URL = "/Api/Advertiser/Reporting/v13/ReportingService.svc";
    private static final String OAUTH_TOKEN_URL = "https://login.microsoftonline.com/common/oauth2/v2.0/token";


    @Resource
    private BingPromotionConfigProperties bingPromotionConfigProperties;

    private Map<String, ApplicationBO> appConfigMap = new HashMap<>();

    @Resource
    private RedisHelper redisHelper;
    @Resource
    private RestApiHelper restApiHelper;
//
//    @PostConstruct
//    public void init() {
//        List<ApplicationBO> applications = JSON.parseArray(appConfigProperties, ApplicationBO.class);
//        appConfigMap = applications.stream().collect(Collectors.toMap(ApplicationBO::getCustomerId, value -> value));
//    }


    // 在BingAppService中添加日志
    @PostConstruct
    public void init() {
        log.info("【Bing配置】配置属性对象: {}", JSON.toJSONString(bingPromotionConfigProperties));
        log.info("【Bing配置】reportDataCustomerId: {}", bingPromotionConfigProperties.getReportDataCustomerId());
        log.info("【Bing配置】limit: {}", bingPromotionConfigProperties.getLimit());

        List<ApplicationBO> applications = bingPromotionConfigProperties.getAppConfig();
        log.info("【Bing配置】应用配置列表: {}", JSON.toJSONString(applications));

        if (applications != null && !applications.isEmpty()) {
            appConfigMap = applications.stream().collect(Collectors.toMap(ApplicationBO::getCustomerId, value -> value));
            log.info("【Bing配置】应用配置Map keys: {}", appConfigMap.keySet());
        } else {
            log.warn("【Bing配置】应用配置为空，请检查配置文件");
        }
    }
    /**
     * 获取当前应用程序的类型。
     */
    @Override
    public String getAppType() {
        return BING.getCode();
    }

    /**
     * 获取 Redis 辅助工具实例。
     */
    @Override
    public RedisHelper getRedisHelper() {
        return redisHelper;
    }

    /**
     * 处理Bing OAuth回调
     * @param param OAuth回调参数
     * @return JSON格式的响应字符串
     */
    public String callback(BingCallbackBO param) {
        log.info("Bing OAuth回调信息: {}", JSON.toJSONString(param));

        // 检查是否有错误
        if (param.hasError()) {
            log.error("Bing OAuth授权失败: {} - {}", param.getError(), param.getError_description());
            return getResponseJson(600011, "授权失败: " + param.getError_description(), null);
        }

        // 检查必要参数
        if (!param.isSuccess()) {
            log.error("Bing OAuth回调参数不完整: {}", JSON.toJSONString(param));
            return getResponseJson(600011, "回调参数不完整", null);
        }

        // 验证state并解析customerId
        String customerId = parseCustomerIdFromState(param.getState());
        if (StringUtils.isBlank(customerId)) {
            log.error("Bing OAuth state验证失败或无法解析customerId: {}", param.getState());
            return getResponseJson(600011, "状态验证失败", null);
        }

        // 获取应用配置
        ApplicationBO application = getAppByCustomerId(customerId);
        if (application == null) {
            log.error("未找到customerId对应的应用配置: {}", customerId);
            return getResponseJson(600011, "应用配置不存在", null);
        }

        // 使用授权码换取访问令牌
        String accessToken = doAccessToken(param.getCode(), application);
        if (StringUtils.isBlank(accessToken)) {
            return getResponseJson(600011, "未获取到 access_token", null);
        }

        return getResponseJson(0, "success", accessToken);
    }

    /**
     * 使用授权码换取访问令牌
     */
    private String doAccessToken(String authCode, ApplicationBO application) {
        try {
            log.info("【Bing OAuth】开始换取访问令牌，customerId: {}", application.getCustomerId());

            // 构建请求参数
            Map<String, String> params = new HashMap<>();
            params.put("grant_type", "authorization_code");
            params.put("client_id", application.getClientId());
            params.put("client_secret", application.getClientSecret());
            params.put("code", authCode);
            params.put("redirect_uri", bingPromotionConfigProperties.getRedirectUri());
            params.put("scope", "https://ads.microsoft.com/msads.manage offline_access");

            // 发送请求
            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/x-www-form-urlencoded");

            String formData = params.entrySet().stream()
                    .map(entry -> entry.getKey() + "=" + entry.getValue())
                    .collect(Collectors.joining("&"));

            String responseJson = restApiHelper.post(OAUTH_TOKEN_URL, formData, headers);
            log.info("【Bing OAuth】换取访问令牌响应: {}", responseJson);

            // 解析响应
            JSONObject response = JSON.parseObject(responseJson);
            if (response.containsKey("error")) {
                log.error("【Bing OAuth】换取访问令牌失败: {}", response.getString("error_description"));
                return null;
            }

            // 保存token到Redis
            AccountTokenBO accountToken = saveAccountTokenInRedis(response, application.getCustomerId());
            return accountToken.getAccessToken();

        } catch (Exception e) {
            log.error("【Bing OAuth】换取访问令牌异常", e);
            return null;
        }
    }

    /**
     * 保存账户令牌到Redis
     */
    private AccountTokenBO saveAccountTokenInRedis(JSONObject tokenResponse, String customerId) {
        // 计算过期时间
        int expiresIn = tokenResponse.getIntValue("expires_in"); // 通常是3600秒
        Date expiresTime = DateTimeUtil.DateAddSecond(expiresIn - 300); // 提前5分钟过期

        // Bing的refresh_token通常有效期很长，设置为90天
        Date refreshExpiresTime = DateTimeUtil.DateAddDayOfYear(90);

        AccountTokenBO accountToken = AccountTokenBO.builder()
                .accessToken(tokenResponse.getString("access_token"))
                .refreshToken(tokenResponse.getString("refresh_token"))
                .expiresTime(expiresTime)
                .refreshExpiresTime(refreshExpiresTime)
                .userId(customerId)
                .openId(customerId)
                .build();

        log.info("redisToken:{}",REDIS_KEY_TOKEN);
        redisHelper.hset(REDIS_KEY_TOKEN, customerId, JSON.toJSONString(accountToken));
        String redis =
                (String)redisHelper.hget(REDIS_KEY_TOKEN,customerId);
        log.info("redis:{}",redis);
        log.info("【Bing OAuth】Token保存成功，customerId: {}", customerId);

        return accountToken;
    }

    /**
     * 从state中解析customerId
     * state格式: bing_{customerId}_{timestamp}
     */
    private String parseCustomerIdFromState(String state) {
        if (StringUtils.isBlank(state)) {
            return null;
        }

        try {
            String[] parts = state.split("_");
            if (parts.length >= 3 && "bing".equals(parts[0])) {
                return parts[1];
            }
        } catch (Exception e) {
            log.warn("解析state失败: {}", state, e);
        }

        return null;
    }

    /**
     * 生成OAuth授权URL
     */
    public String generateAuthUrl(String customerId) {
        ApplicationBO application = getAppByCustomerId(customerId);
        if (application == null) {
            log.error("未找到customerId对应的应用配置: {}", customerId);
            return null;
        }

        // 生成state: bing_{customerId}_{timestamp}
        String state = "bing_" + customerId + "_" + System.currentTimeMillis();

        // 构建授权URL
        String authUrl = "https://login.microsoftonline.com/common/oauth2/v2.0/authorize" +
                "?client_id=" + application.getClientId() +
                "&response_type=code" +
                "&redirect_uri=" + bingPromotionConfigProperties.getRedirectUri() +
                "&scope=https://ads.microsoft.com/msads.manage offline_access" +
                "&state=" + state;

        log.info("【Bing OAuth】生成授权URL，customerId: {}, authUrl: {}", customerId, authUrl);
        return authUrl;
    }

    /**
     * 生成响应JSON
     */
    private String getResponseJson(int code, String message, String data) {
        JSONObject response = new JSONObject();
        response.put("code", code);
        response.put("message", message);
        response.put("data", data);
        return response.toJSONString();
    }

    /**
     * 刷新指定应用程序ID的访问令牌。
     */
    @Override
    public void doRefreshToken(String customerId) {
        log.info("【Bing广告】刷新accessToken：{}", customerId);

        log.info("customerId:{}",customerId);
        // 从redis中获取缓存数据
        String accountTokenJson = (String) getRedisHelper().hget(REDIS_KEY_TOKEN, customerId);
        log.info("redisToken:{}|{}",REDIS_KEY_TOKEN, accountTokenJson);
        if (StringUtils.isBlank(accountTokenJson)) {
            log.error("Bing广告刷新accessToken需要有生效状态的refreshToken字段1，当前不存在，请重新授权, customerId：{}", customerId);
            return;
        }

        AccountTokenBO accountToken = JSON.parseObject(accountTokenJson, AccountTokenBO.class);
        if (accountToken.getRefreshExpiresTime().before(new Date())) {
            log.error("Bing广告刷新accessToken需要有生效状态的refreshToken字段2，当前已失效，请重新授权, customerId：{}", customerId);
            return;
        }

        // 查询该customerId的应用信息
        ApplicationBO application = getAppByCustomerId(customerId);
        if (application == null) {
            log.error("Bing广告刷新accessToken失败，未找到customerId对应的应用配置: {}", customerId);
            return;
        }

        try {
            // 构建刷新令牌请求参数
            Map<String, String> params = new HashMap<>();
            params.put("grant_type", "refresh_token");
            params.put("client_id", application.getClientId());
            params.put("client_secret", application.getClientSecret());
            params.put("refresh_token", accountToken.getRefreshToken());
            params.put("scope", "https://ads.microsoft.com/msads.manage offline_access");

            // 发送请求
            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/x-www-form-urlencoded");

            String formData = params.entrySet().stream()
                    .map(entry -> entry.getKey() + "=" + entry.getValue())
                    .collect(Collectors.joining("&"));

            String responseJson = restApiHelper.post(OAUTH_TOKEN_URL, formData, headers);
            log.info("【Bing广告】刷新accessToken响应: {}", responseJson);

            // 解析响应
            JSONObject response = JSON.parseObject(responseJson);
            if (response.containsKey("error")) {
                log.error("【Bing广告】刷新accessToken失败: {}", response.getString("error_description"));
                return;
            }

            // 保存新的token到Redis
            saveAccountTokenInRedis(response, customerId);
            log.info("【Bing广告】刷新accessToken成功：{}", customerId);

        } catch (Exception e) {
            log.error("【Bing广告】刷新accessToken异常", e);
        }
    }

    /**
     * 查找搜索日志记录。
     */
    @Override
    public RspList<RpSearchLog> findRpSearchLog(Date startDate, Date endDate, PromotionTypeEnum promotionType, int page) {
        log.info("【Bing广告】查找搜索日志记录：startDate-{}, endDate-{}, promotionType-{}, page-{}",
                startDate, endDate, promotionType, page);

        if (page < 1) {
            page = 1;
        }

        // 检查配置
        if (bingPromotionConfigProperties == null) {
            log.error("Bing推广配置为空");
            return BaseRspUtils.createErrorRspList("Bing推广配置为空");
        }

        String reportDataCustomerId = bingPromotionConfigProperties.getReportDataCustomerId();
        if (StringUtils.isBlank(reportDataCustomerId)) {
            log.error("Bing报告数据客户ID为空");
            return BaseRspUtils.createErrorRspList("Bing报告数据客户ID为空");
        }

        // 从第几行开始获取数据
        int offset = (page - 1) * bingPromotionConfigProperties.getLimit();
        ApplicationBO application = getAppByCustomerId(reportDataCustomerId);

        if (application == null) {
            log.error("Bing广告查找搜索日志记录失败，未找到customerId对应的应用配置: {}", reportDataCustomerId);
            return BaseRspUtils.createErrorRspList("应用配置不存在，customerId: " + reportDataCustomerId);
        }

        log.info("【Bing广告】使用应用配置: customerId={}, developerToken={}, clientId={}",
                application.getCustomerId(), application.getDeveloperToken(), application.getClientId());

        // 构建SOAP请求体（内部会获取访问令牌）
        String soapRequest;
        try {
            soapRequest = buildSearchLogReportRequest(
                application, startDate, endDate, promotionType, offset, bingPromotionConfigProperties.getLimit());
            log.info("【Bing广告】构建的SOAP请求长度: {}", soapRequest.length());
        } catch (Exception e) {
            log.error("【Bing广告】构建SOAP请求失败", e);
            return BaseRspUtils.createErrorRspList("构建SOAP请求失败: " + e.getMessage());
        }

        // 获取访问令牌用于HTTP请求头
        String accessToken = getAccessToken(reportDataCustomerId);
        if (StringUtils.isBlank(accessToken)) {
            log.error("获取访问令牌失败，customerId: {}", reportDataCustomerId);
            return BaseRspUtils.createErrorRspList("获取访问令牌失败");
        }

        String reportRequestId;
        try {
            log.info("【Bing广告】提交报告生成请求开始，URL: {}", BING_API_BASE_URL + "/Reporting/v13/ReportingService.svc");

            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "text/xml; charset=utf-8");
            headers.set("SOAPAction", "SubmitGenerateReport");
            headers.set("Authorization", "Bearer " + accessToken);
            headers.set("User-Agent", "NBChat-BingAds-Client/1.0");

            String submitResponse = restApiHelper.post(BING_API_BASE_URL + REPORT_SERVICE_URL, soapRequest, headers);
            log.info("【Bing广告】提交报告生成请求结束，响应长度: {}", submitResponse != null ? submitResponse.length() : 0);

            // 检查是否有错误
            if (submitResponse.contains("soap:Fault") || submitResponse.contains("s:Fault")) {
                log.error("【Bing广告】提交报告生成请求失败，SOAP错误: {}", submitResponse);
                return BaseRspUtils.createErrorRspList("提交报告生成请求失败，SOAP错误");
            }

            // 提取报告请求ID
            reportRequestId = BingReportUtils.extractReportRequestId(submitResponse);
            if (StringUtils.isBlank(reportRequestId)) {
                log.error("【Bing广告】未能获取报告请求ID，响应: {}", submitResponse);
                return BaseRspUtils.createErrorRspList("未能获取报告请求ID");
            }

            log.info("【Bing广告】成功获取报告请求ID: {}", reportRequestId);

        } catch (Exception e) {
            log.error("【Bing广告】提交报告生成请求失败", e);
            return BaseRspUtils.createErrorRspList("提交报告生成请求失败: " + e.getMessage());
        }

        // 轮询报告状态并下载数据
        return pollAndDownloadReport(application, accessToken, reportRequestId, promotionType);
    }

    /**
     * 轮询报告状态并下载数据
     */
    private RspList<RpSearchLog> pollAndDownloadReport(ApplicationBO application, String accessToken,
                                                      String reportRequestId, PromotionTypeEnum promotionType) {
        try {
            log.info("【Bing广告】开始轮询报告状态，reportRequestId: {}", reportRequestId);

            // 轮询报告状态，最多等待5分钟
            int maxAttempts = 30; // 每10秒轮询一次，最多30次
            int attempt = 0;

            while (attempt < maxAttempts) {
                Thread.sleep(10000); // 等待10秒
                attempt++;

                // 构建轮询请求
                String pollRequest = BingReportUtils.buildPollGenerateReportRequest(
                    application.getCustomerId(), application.getDeveloperToken(),
                    application.getCustomerAccountId(), accessToken, reportRequestId);

                HttpHeaders headers = new HttpHeaders();
                headers.set("Content-Type", "text/xml; charset=utf-8");
                headers.set("SOAPAction", "PollGenerateReport");
                headers.set("Authorization", "Bearer " + accessToken);
                headers.set("User-Agent", "NBChat-BingAds-Client/1.0");

                try {
                    String pollResponse = restApiHelper.post(BING_API_BASE_URL + REPORT_SERVICE_URL, pollRequest, headers);
                    log.info("【Bing广告】轮询报告状态 (第{}次)，响应长度: {}", attempt, pollResponse != null ? pollResponse.length() : 0);

                    // 检查SOAP错误
                    if (pollResponse.contains("soap:Fault") || pollResponse.contains("s:Fault")) {
                        log.error("【Bing广告】轮询报告状态SOAP错误: {}", pollResponse);
                        return BaseRspUtils.createErrorRspList("轮询报告状态SOAP错误");
                    }

                    // 解析状态
                    BingReportUtils.ReportStatus status = BingReportUtils.extractReportStatus(pollResponse);
                    if (status == null) {
                        log.error("【Bing广告】解析报告状态失败，响应: {}", pollResponse);
                        continue;
                    }

                    log.info("【Bing广告】报告状态: {}, 下载URL: {}", status.getStatus(), status.getDownloadUrl());

                    if (status.isSuccess() && StringUtils.isNotBlank(status.getDownloadUrl())) {
                        // 报告生成成功，下载数据
                        log.info("【Bing广告】报告生成成功，开始下载: {}", status.getDownloadUrl());
                        return downloadAndParseReport(status.getDownloadUrl(), promotionType);
                    } else if (status.isError()) {
                        log.error("【Bing广告】报告生成失败，状态: {}", status.getStatus());
                        return BaseRspUtils.createErrorRspList("报告生成失败");
                    } else if (status.isPending()) {
                        log.info("【Bing广告】报告正在生成中，继续等待... (第{}次)", attempt);
                        continue;
                    } else {
                        log.warn("【Bing广告】未知的报告状态: {}", status.getStatus());
                    }
                } catch (Exception e) {
                    log.error("【Bing广告】轮询报告状态请求失败 (第{}次)", attempt, e);
                    if (attempt >= maxAttempts) {
                        return BaseRspUtils.createErrorRspList("轮询报告状态请求失败: " + e.getMessage());
                    }
                    // 继续尝试
                }
            }

            log.error("【Bing广告】报告生成超时，已尝试{}次", maxAttempts);
            return BaseRspUtils.createErrorRspList("报告生成超时");

        } catch (Exception e) {
            log.error("【Bing广告】轮询报告状态失败", e);
            return BaseRspUtils.createErrorRspList("轮询报告状态失败: " + e.getMessage());
        }
    }

    /**
     * 下载并解析报告数据
     */
    private RspList<RpSearchLog> downloadAndParseReport(String downloadUrl, PromotionTypeEnum promotionType) {
        try {
            log.info("【Bing广告】开始下载报告数据，URL: {}", downloadUrl);

            List<String[]> csvData = BingReportUtils.downloadCsvReport(downloadUrl);
            log.info("【Bing广告】成功下载CSV数据，行数: {}", csvData.size());

            List<RpSearchLog> rpSearchLogs = new ArrayList<>();

            for (int i = 0; i < csvData.size(); i++) {
                String[] row = csvData.get(i);
                try {
                    RpSearchLog rpSearchLog = parseCsvRow(row, promotionType);
                    if (rpSearchLog != null) {
                        rpSearchLogs.add(rpSearchLog);
                    }
                } catch (Exception e) {
                    log.warn("【Bing广告】解析CSV第{}行失败: {}", i + 1, Arrays.toString(row), e);
                }
            }

            log.info("【Bing广告】成功解析搜索日志记录数: {}", rpSearchLogs.size());
            return BaseRspUtils.createSuccessRspList(rpSearchLogs, (long) rpSearchLogs.size());

        } catch (Exception e) {
            log.error("【Bing广告】下载并解析报告数据失败", e);
            return BaseRspUtils.createErrorRspList("下载并解析报告数据失败: " + e.getMessage());
        }
    }

    /**
     * 解析CSV行数据
     */
    private RpSearchLog parseCsvRow(String[] row, PromotionTypeEnum promotionType) {
        try {
            if (row.length < 12) {
                log.warn("CSV行数据不完整: {}", Arrays.toString(row));
                return null;
            }

            RpSearchLog rpSearchLog = new RpSearchLog();

            // CSV列顺序: TimePeriod, SearchQuery, CampaignName, AdGroupName, Keyword,
            //           Impressions, Clicks, Spend, Ctr, AverageCpc, DeviceType, MatchType

            // 设置日期
            String timePeriod = row[0];
            if (StringUtils.isNotBlank(timePeriod)) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    rpSearchLog.setDayData(sdf.parse(timePeriod));
                } catch (Exception e) {
                    log.warn("解析日期失败: {}", timePeriod);
                    rpSearchLog.setDayData(new Date());
                }
            } else {
                rpSearchLog.setDayData(new Date());
            }

            // 设置来源
            if (promotionType != null && !PromotionTypeEnum.ALL_PROMOTION_TYPE.equals(promotionType)) {
                rpSearchLog.setSources("Bing".concat(promotionType.getDesc()));
            } else {
                String deviceType = row[10]; // DeviceType列
                if ("Computer".equals(deviceType)) {
                    rpSearchLog.setSources("BingPC端");
                } else if ("Smartphone".equals(deviceType) || "Tablet".equals(deviceType)) {
                    rpSearchLog.setSources("Bing移动端");
                } else {
                    rpSearchLog.setSources("Bing");
                }
            }

            // 设置其他字段
            rpSearchLog.setTerm(row[1]); // SearchQuery
            rpSearchLog.setPlanScheme(row[2]); // CampaignName
            rpSearchLog.setKeywordGroups(row[3]); // AdGroupName
            rpSearchLog.setKeyword(row[4]); // Keyword

            // 数值字段
            rpSearchLog.setImpressions(parseIntValue(row[5])); // Impressions
            rpSearchLog.setClick(parseIntValue(row[6])); // Clicks

            // 消费 (美元转分)
            BigDecimal spend = parseBigDecimalValue(row[7]);
            rpSearchLog.setConsumption(spend.multiply(BigDecimal.valueOf(100)).intValue());

            // 点击率 (百分比转万分比)
            BigDecimal ctr = parseBigDecimalValue(row[8].replace("%", ""));
            rpSearchLog.setClickRate(ctr.multiply(BigDecimal.valueOf(100)).intValue());

            // 平均点击价格 (美元转分)
            BigDecimal avgCpc = parseBigDecimalValue(row[9]);
            rpSearchLog.setAvgClickPrice(avgCpc.multiply(BigDecimal.valueOf(100)).intValue());

            // 匹配模式
            rpSearchLog.setMatchMode(row[11]); // MatchType

            return rpSearchLog;

        } catch (Exception e) {
            log.error("解析CSV行数据失败: {}", Arrays.toString(row), e);
            return null;
        }
    }

    /**
     * 解析整数值
     */
    private int parseIntValue(String value) {
        if (StringUtils.isBlank(value) || "--".equals(value)) {
            return 0;
        }
        try {
            return Integer.parseInt(value.replace(",", ""));
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    /**
     * 解析BigDecimal值
     */
    private BigDecimal parseBigDecimalValue(String value) {
        if (StringUtils.isBlank(value) || "--".equals(value)) {
            return BigDecimal.ZERO;
        }
        try {
            return new BigDecimal(value.replace(",", ""));
        } catch (NumberFormatException e) {
            return BigDecimal.ZERO;
        }
    }

    /**
     * 根据customerId获取应用配置（公开方法，用于测试）
     */
    public ApplicationBO getAppByCustomerId(String customerId) {
        return appConfigMap.get(customerId);
    }

    /**
     * 构建搜索日志报告请求（用于测试，不获取实际访问令牌）
     */
    public String buildSearchLogReportRequestForTest(ApplicationBO application, Date startDate, Date endDate,
                                                    PromotionTypeEnum promotionType, int offset, int limit) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        log.info("【Bing广告】构建测试SOAP请求，customerId: {}, developerToken: {}, customerAccountId: {}, 日期范围: {} 到 {}, 推广类型: {}",
                application.getCustomerId(), application.getDeveloperToken(), application.getCustomerAccountId(),
                sdf.format(startDate), sdf.format(endDate), promotionType);

        // 使用示例访问令牌
        String accessToken = "EXAMPLE_ACCESS_TOKEN_HERE";

        StringBuilder soapBody = new StringBuilder();
        soapBody.append("<?xml version=\"1.0\" encoding=\"utf-8\"?>");
        soapBody.append("<s:Envelope xmlns:s=\"http://schemas.xmlsoap.org/soap/envelope/\">");

        // 修正的SOAP Header，使用Microsoft Advertising Reporting API v13规范
        soapBody.append("<s:Header xmlns:h=\"https://bingads.microsoft.com/Reporting/v13\">");

        // 可选：操作名称
        soapBody.append("<Action mustUnderstand=\"1\">SubmitGenerateReport</Action>");

        // 必填：OAuth access_token
        soapBody.append("<AuthenticationToken i:nil=\"false\" >").append(accessToken).append("</AuthenticationToken>");

        // 必填：CustomerId（经理账号ID）
        soapBody.append("<CustomerId i:nil=\"false\" >").append(application.getCustomerId()).append("</CustomerId>");

        // 必填：CustomerAccountId（广告账户ID）
        if (StringUtils.isNotBlank(application.getCustomerAccountId())) {
            soapBody.append("<CustomerAccountId i:nil=\"false\">").append(application.getCustomerAccountId()).append("</CustomerAccountId>");
        } else {
            soapBody.append("<!-- WARNING: CustomerAccountId为空，可能导致权限不足错误 -->");
        }

        // 必填：开发者令牌
        soapBody.append("<DeveloperToken i:nil=\"false\">").append(application.getDeveloperToken()).append("</DeveloperToken>");





        soapBody.append("</s:Header>");

        soapBody.append("<s:Body>");
        soapBody.append("<SubmitGenerateReportRequest xmlns=\"https://bingads.microsoft.com/Reporting/v13\">");
        soapBody.append("<ReportRequest xmlns:i=\"http://www.w3.org/2001/XMLSchema-instance\" i:type=\"SearchQueryPerformanceReportRequest\">");

        // 基本参数
        soapBody.append("<ReportName>SearchQueryPerformanceReport</ReportName>");
        soapBody.append("<Format>Csv</Format>");
        soapBody.append("<Language>English</Language>");
        soapBody.append("<ReturnOnlyCompleteData>false</ReturnOnlyCompleteData>");
        soapBody.append("<ExcludeReportHeader>true</ExcludeReportHeader>");
        soapBody.append("<ExcludeReportFooter>true</ExcludeReportFooter>");
        soapBody.append("<ExcludeColumnHeaders>false</ExcludeColumnHeaders>");

        // 时间范围
        soapBody.append("<Time>");
        soapBody.append("<CustomDateRangeStart>");
        String[] startParts = sdf.format(startDate).split("-");
        soapBody.append("<Year>").append(Integer.parseInt(startParts[0])).append("</Year>");
        soapBody.append("<Month>").append(Integer.parseInt(startParts[1])).append("</Month>");
        soapBody.append("<Day>").append(Integer.parseInt(startParts[2])).append("</Day>");
        soapBody.append("</CustomDateRangeStart>");
        soapBody.append("<CustomDateRangeEnd>");
        String[] endParts = sdf.format(endDate).split("-");
        soapBody.append("<Year>").append(Integer.parseInt(endParts[0])).append("</Year>");
        soapBody.append("<Month>").append(Integer.parseInt(endParts[1])).append("</Month>");
        soapBody.append("<Day>").append(Integer.parseInt(endParts[2])).append("</Day>");
        soapBody.append("</CustomDateRangeEnd>");
        soapBody.append("</Time>");

        // 可选：限定账户范围
        if (StringUtils.isNotBlank(application.getCustomerAccountId())) {
            soapBody.append("<Scope>");
            soapBody.append("<AccountIds xmlns:i=\"http://www.w3.org/2001/XMLSchema-instance\" i:type=\"tns:ArrayOflong\" xmlns:tns=\"https://bingads.microsoft.com/Reporting/v13\">");
            soapBody.append("<long>").append(application.getCustomerAccountId()).append("</long>");
            soapBody.append("</AccountIds>");
            soapBody.append("</Scope>");
        }

        // 列定义（使用v13正确的列名）
        soapBody.append("<Columns>");
        soapBody.append("<SearchQueryPerformanceReportColumn>TimePeriod</SearchQueryPerformanceReportColumn>");
        soapBody.append("<SearchQueryPerformanceReportColumn>SearchQuery</SearchQueryPerformanceReportColumn>");
        soapBody.append("<SearchQueryPerformanceReportColumn>CampaignName</SearchQueryPerformanceReportColumn>");
        soapBody.append("<SearchQueryPerformanceReportColumn>AdGroupName</SearchQueryPerformanceReportColumn>");
        soapBody.append("<SearchQueryPerformanceReportColumn>Keyword</SearchQueryPerformanceReportColumn>");
        soapBody.append("<SearchQueryPerformanceReportColumn>Impressions</SearchQueryPerformanceReportColumn>");
        soapBody.append("<SearchQueryPerformanceReportColumn>Clicks</SearchQueryPerformanceReportColumn>");
        soapBody.append("<SearchQueryPerformanceReportColumn>Spend</SearchQueryPerformanceReportColumn>");
        soapBody.append("<SearchQueryPerformanceReportColumn>Ctr</SearchQueryPerformanceReportColumn>");
        soapBody.append("<SearchQueryPerformanceReportColumn>AverageCpc</SearchQueryPerformanceReportColumn>");
        soapBody.append("<SearchQueryPerformanceReportColumn>DeviceType</SearchQueryPerformanceReportColumn>");
        // 使用v13正确的列名：DeliveredMatchType 而不是 MatchType
        soapBody.append("<SearchQueryPerformanceReportColumn>DeliveredMatchType</SearchQueryPerformanceReportColumn>");
        soapBody.append("</Columns>");

        // 过滤器
        if (promotionType != null && !ALL_PROMOTION_TYPE.equals(promotionType)) {
            soapBody.append("<Filter>");
            soapBody.append("<DeviceType>").append(promotionType.getBingType()).append("</DeviceType>");
            soapBody.append("</Filter>");
        }

        soapBody.append("</ReportRequest>");
        soapBody.append("</SubmitGenerateReportRequest>");
        soapBody.append("</s:Body>");
        soapBody.append("</s:Envelope>");

        String result = soapBody.toString();
        log.debug("【Bing广告】生成的测试SOAP请求: {}", result);
        return result;
    }

    /**
     * 构建搜索日志报告请求
     */
    private String buildSearchLogReportRequest(ApplicationBO application, Date startDate, Date endDate,
                                             PromotionTypeEnum promotionType, int offset, int limit) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        log.info("【Bing广告】构建SOAP请求，customerId: {}, developerToken: {}, customerAccountId: {}, 日期范围: {} 到 {}, 推广类型: {}",
                application.getCustomerId(), application.getDeveloperToken(), application.getCustomerAccountId(),
                sdf.format(startDate), sdf.format(endDate), promotionType);

        // 获取访问令牌
        String accessToken = getAccessToken(application.getCustomerId());
        if (StringUtils.isBlank(accessToken)) {
            log.error("【Bing广告】构建SOAP请求时获取访问令牌失败");
            throw new RuntimeException("获取访问令牌失败");
        }

        StringBuilder soapBody = new StringBuilder();
        soapBody.append("<?xml version=\"1.0\" encoding=\"utf-8\"?>");
        soapBody.append("<s:Envelope xmlns:s=\"http://schemas.xmlsoap.org/soap/envelope/\">");

        // 修正的SOAP Header，使用Microsoft Advertising Reporting API v13规范
        soapBody.append("<s:Header xmlns:h=\"https://bingads.microsoft.com/Reporting/v13\">");

        // 可选：操作名称
        soapBody.append("<Action mustUnderstand=\"1\">SubmitGenerateReport</Action>");

        // 必填：OAuth access_token
        soapBody.append("<AuthenticationToken>").append(accessToken).append("</AuthenticationToken>");

        // 必填：CustomerId（经理账号ID）
        soapBody.append("<CustomerId>").append(application.getCustomerId()).append("</CustomerId>");

        // 必填：CustomerAccountId（广告账户ID）
        if (StringUtils.isNotBlank(application.getCustomerAccountId())) {
            soapBody.append("<CustomerAccountId>").append(application.getCustomerAccountId()).append("</CustomerAccountId>");
        } else {
            log.warn("【Bing广告】CustomerAccountId为空，可能导致权限不足错误");
        }

        // 必填：开发者令牌
        soapBody.append("<DeveloperToken>").append(application.getDeveloperToken()).append("</DeveloperToken>");

        soapBody.append("</s:Header>");
        soapBody.append("<s:Body>");
        soapBody.append("<SubmitGenerateReportRequest xmlns=\"https://bingads.microsoft.com/Reporting/v13\">");
        soapBody.append("<ReportRequest xmlns:i=\"http://www.w3.org/2001/XMLSchema-instance\" i:type=\"SearchQueryPerformanceReportRequest\">");

        // 基本参数
        soapBody.append("<ReportName>SearchQueryPerformanceReport</ReportName>");
        soapBody.append("<Format>Csv</Format>");
        soapBody.append("<Language>English</Language>");
        soapBody.append("<ReturnOnlyCompleteData>false</ReturnOnlyCompleteData>");
        soapBody.append("<ExcludeReportHeader>true</ExcludeReportHeader>");
        soapBody.append("<ExcludeReportFooter>true</ExcludeReportFooter>");
        soapBody.append("<ExcludeColumnHeaders>false</ExcludeColumnHeaders>");

        // 时间范围
        soapBody.append("<Time>");
        soapBody.append("<CustomDateRangeStart>");
        String[] startParts = sdf.format(startDate).split("-");
        soapBody.append("<Year>").append(Integer.parseInt(startParts[0])).append("</Year>");
        soapBody.append("<Month>").append(Integer.parseInt(startParts[1])).append("</Month>");
        soapBody.append("<Day>").append(Integer.parseInt(startParts[2])).append("</Day>");
        soapBody.append("</CustomDateRangeStart>");
        soapBody.append("<CustomDateRangeEnd>");
        String[] endParts = sdf.format(endDate).split("-");
        soapBody.append("<Year>").append(Integer.parseInt(endParts[0])).append("</Year>");
        soapBody.append("<Month>").append(Integer.parseInt(endParts[1])).append("</Month>");
        soapBody.append("<Day>").append(Integer.parseInt(endParts[2])).append("</Day>");
        soapBody.append("</CustomDateRangeEnd>");
        soapBody.append("</Time>");

        // 可选：限定账户范围
        if (StringUtils.isNotBlank(application.getCustomerAccountId())) {
            soapBody.append("<Scope>");
            soapBody.append("<AccountIds xmlns:i=\"http://www.w3.org/2001/XMLSchema-instance\" i:type=\"tns:ArrayOflong\" xmlns:tns=\"https://bingads.microsoft.com/Reporting/v13\">");
            soapBody.append("<long>").append(application.getCustomerAccountId()).append("</long>");
            soapBody.append("</AccountIds>");
            soapBody.append("</Scope>");
            log.info("【Bing广告】添加账户范围限制: {}", application.getCustomerAccountId());
        }

        // 列定义（使用v13正确的列名）
        soapBody.append("<Columns>");
        soapBody.append("<SearchQueryPerformanceReportColumn>TimePeriod</SearchQueryPerformanceReportColumn>");
        soapBody.append("<SearchQueryPerformanceReportColumn>SearchQuery</SearchQueryPerformanceReportColumn>");
        soapBody.append("<SearchQueryPerformanceReportColumn>CampaignName</SearchQueryPerformanceReportColumn>");
        soapBody.append("<SearchQueryPerformanceReportColumn>AdGroupName</SearchQueryPerformanceReportColumn>");
        soapBody.append("<SearchQueryPerformanceReportColumn>Keyword</SearchQueryPerformanceReportColumn>");
        soapBody.append("<SearchQueryPerformanceReportColumn>Impressions</SearchQueryPerformanceReportColumn>");
        soapBody.append("<SearchQueryPerformanceReportColumn>Clicks</SearchQueryPerformanceReportColumn>");
        soapBody.append("<SearchQueryPerformanceReportColumn>Spend</SearchQueryPerformanceReportColumn>");
        soapBody.append("<SearchQueryPerformanceReportColumn>Ctr</SearchQueryPerformanceReportColumn>");
        soapBody.append("<SearchQueryPerformanceReportColumn>AverageCpc</SearchQueryPerformanceReportColumn>");
        soapBody.append("<SearchQueryPerformanceReportColumn>DeviceType</SearchQueryPerformanceReportColumn>");
        // 使用v13正确的列名：DeliveredMatchType 而不是 MatchType
        soapBody.append("<SearchQueryPerformanceReportColumn>DeliveredMatchType</SearchQueryPerformanceReportColumn>");
        soapBody.append("</Columns>");

        // 过滤器
        if (promotionType != null && !ALL_PROMOTION_TYPE.equals(promotionType)) {
            soapBody.append("<Filter>");
            soapBody.append("<DeviceType>").append(promotionType.getBingType()).append("</DeviceType>");
            soapBody.append("</Filter>");
            log.info("【Bing广告】添加设备类型过滤器: {}", promotionType.getBingType());
        }

        soapBody.append("</ReportRequest>");
        soapBody.append("</SubmitGenerateReportRequest>");
        soapBody.append("</s:Body>");
        soapBody.append("</s:Envelope>");

        String result = soapBody.toString();
        log.debug("【Bing广告】生成的SOAP请求: {}", result);
        return result;
    }

    /**
     * 解析搜索日志响应
     */
    private RspList<RpSearchLog> parseSearchLogResponse(String responseXml, PromotionTypeEnum promotionType) {
        try {
            // 检查是否有SOAP错误
            if (responseXml.contains("soap:Fault") || responseXml.contains("s:Fault")) {
                log.error("Bing广告API返回错误: {}", responseXml);
                return BaseRspUtils.createErrorRspList("API调用失败");
            }

            List<RpSearchLog> rpSearchLogs = new ArrayList<>();
            long totalCount = 0;

            // 解析XML响应
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new ByteArrayInputStream(responseXml.getBytes("UTF-8")));

            // 查找报告数据
            NodeList reportDataNodes = document.getElementsByTagName("ReportData");
            if (reportDataNodes.getLength() == 0) {
                log.warn("Bing广告响应中未找到ReportData节点");
                return BaseRspUtils.createSuccessRspList(rpSearchLogs, totalCount);
            }

            Element reportDataElement = (Element) reportDataNodes.item(0);

            // 获取总数
            NodeList totalCountNodes = reportDataElement.getElementsByTagName("TotalRowCount");
            if (totalCountNodes.getLength() > 0) {
                totalCount = Long.parseLong(totalCountNodes.item(0).getTextContent());
            }

            // 解析数据行
            NodeList rowNodes = reportDataElement.getElementsByTagName("Row");
            for (int i = 0; i < rowNodes.getLength(); i++) {
                Element rowElement = (Element) rowNodes.item(i);
                RpSearchLog rpSearchLog = parseSearchLogRow(rowElement, promotionType);
                if (rpSearchLog != null) {
                    rpSearchLogs.add(rpSearchLog);
                }
            }

            return BaseRspUtils.createSuccessRspList(rpSearchLogs, totalCount);

        } catch (Exception e) {
            log.error("解析Bing广告响应失败", e);
            return BaseRspUtils.createErrorRspList("解析响应失败");
        }
    }

    /**
     * 解析单行搜索日志数据
     */
    private RpSearchLog parseSearchLogRow(Element rowElement, PromotionTypeEnum promotionType) {
        try {
            RpSearchLog rpSearchLog = new RpSearchLog();

            // 设置来源
            if (promotionType != null && !PromotionTypeEnum.ALL_PROMOTION_TYPE.equals(promotionType)) {
                rpSearchLog.setSources("Bing".concat(promotionType.getDesc()));
            } else {
                String deviceType = getElementText(rowElement, "DeviceType");
                if ("Computer".equals(deviceType)) {
                    rpSearchLog.setSources("BingPC端");
                } else if ("Smartphone".equals(deviceType) || "Tablet".equals(deviceType)) {
                    rpSearchLog.setSources("Bing移动端");
                } else {
                    rpSearchLog.setSources("Bing");
                }
            }

            // 设置搜索词
            rpSearchLog.setTerm(getElementText(rowElement, "SearchQuery"));

            // 设置日期 - 从TimePeriod字段解析
            String timePeriod = getElementText(rowElement, "TimePeriod");
            if (StringUtils.isNotBlank(timePeriod)) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    rpSearchLog.setDayData(sdf.parse(timePeriod));
                } catch (Exception e) {
                    log.warn("解析日期失败: {}", timePeriod);
                    rpSearchLog.setDayData(new Date());
                }
            } else {
                rpSearchLog.setDayData(new Date());
            }

            // 设置计划名称
            rpSearchLog.setPlanScheme(getElementText(rowElement, "CampaignName"));

            // 设置推广单元
            rpSearchLog.setKeywordGroups(getElementText(rowElement, "AdGroupName"));

            // 设置关键词
            rpSearchLog.setKeyword(getElementText(rowElement, "Keyword"));

            // 设置展现量
            String impressions = getElementText(rowElement, "Impressions");
            rpSearchLog.setImpressions(StringUtils.isNotBlank(impressions) ? Integer.parseInt(impressions) : 0);

            // 设置点击量
            String clicks = getElementText(rowElement, "Clicks");
            rpSearchLog.setClick(StringUtils.isNotBlank(clicks) ? Integer.parseInt(clicks) : 0);

            // 设置消费 (Bing返回的是美元，需要转换为分)
            String spend = getElementText(rowElement, "Spend");
            if (StringUtils.isNotBlank(spend)) {
                BigDecimal spendDecimal = new BigDecimal(spend);
                rpSearchLog.setConsumption(spendDecimal.multiply(BigDecimal.valueOf(100)).intValue());
            } else {
                rpSearchLog.setConsumption(0);
            }

            // 设置点击率 (Bing返回的是百分比，需要转换为万分比)
            String ctr = getElementText(rowElement, "Ctr");
            if (StringUtils.isNotBlank(ctr)) {
                BigDecimal ctrDecimal = new BigDecimal(ctr.replace("%", ""));
                rpSearchLog.setClickRate(ctrDecimal.multiply(BigDecimal.valueOf(100)).intValue());
            } else {
                // 如果没有CTR，根据点击量和展现量计算
                if (rpSearchLog.getImpressions() > 0) {
                    BigDecimal calculatedCtr = BigDecimal.valueOf(rpSearchLog.getClick())
                            .multiply(BigDecimal.valueOf(10000))
                            .divide(BigDecimal.valueOf(rpSearchLog.getImpressions()), BigDecimal.ROUND_HALF_UP);
                    rpSearchLog.setClickRate(calculatedCtr.intValue());
                } else {
                    rpSearchLog.setClickRate(0);
                }
            }

            // 设置平均点击价格 (Bing返回的是美元，需要转换为分)
            String avgCpc = getElementText(rowElement, "AverageCpc");
            if (StringUtils.isNotBlank(avgCpc)) {
                BigDecimal avgCpcDecimal = new BigDecimal(avgCpc);
                rpSearchLog.setAvgClickPrice(avgCpcDecimal.multiply(BigDecimal.valueOf(100)).intValue());
            } else {
                // 如果没有平均CPC，根据消费和点击量计算
                if (rpSearchLog.getClick() > 0) {
                    BigDecimal calculatedAvgCpc = BigDecimal.valueOf(rpSearchLog.getConsumption())
                            .divide(BigDecimal.valueOf(rpSearchLog.getClick()), BigDecimal.ROUND_HALF_UP);
                    rpSearchLog.setAvgClickPrice(calculatedAvgCpc.intValue());
                } else {
                    rpSearchLog.setAvgClickPrice(0);
                }
            }

            // 设置匹配模式
            rpSearchLog.setMatchMode(getElementText(rowElement, "MatchType"));

            return rpSearchLog;

        } catch (Exception e) {
            log.error("解析搜索日志行数据失败", e);
            return null;
        }
    }

    /**
     * 获取XML元素的文本内容
     */
    private String getElementText(Element parent, String tagName) {
        NodeList nodeList = parent.getElementsByTagName(tagName);
        if (nodeList.getLength() > 0) {
            Node node = nodeList.item(0);
            return node.getTextContent();
        }
        return null;
    }
}
