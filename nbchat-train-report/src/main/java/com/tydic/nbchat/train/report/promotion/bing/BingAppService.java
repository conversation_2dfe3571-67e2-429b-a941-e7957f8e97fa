package com.tydic.nbchat.train.report.promotion.bing;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.train.mapper.po.RpSearchLog;
import com.tydic.nbchat.train.report.promotion.AppService;
import com.tydic.nbchat.train.report.promotion.bing.bo.ApplicationBO;
import com.tydic.nbchat.train.report.promotion.bing.bo.BingCallbackBO;
import com.tydic.nbchat.train.report.promotion.bing.config.BingPromotionConfigProperties;
import com.tydic.nbchat.train.report.promotion.bo.AccountTokenBO;
import com.tydic.nbchat.train.report.promotion.bo.PromotionTypeEnum;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.http.RestApiHelper;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.tydic.nbchat.train.report.promotion.bo.AppServiceEnum.BING;
import static com.tydic.nbchat.train.report.promotion.bo.PromotionTypeEnum.ALL_PROMOTION_TYPE;

/**
 * <AUTHOR>
 * @date 2025/6/26
 * @description Bing广告三方推广服务 - 使用REST API
 */
@Slf4j
@Service
public class BingAppService extends AppService {

    private static final String OAUTH_TOKEN_URL = "https://login.microsoftonline.com/common/oauth2/v2.0/token";

    @Resource
    private BingPromotionConfigProperties bingPromotionConfigProperties;

    private Map<String, ApplicationBO> appConfigMap = new HashMap<>();

    @Resource
    private RedisHelper redisHelper;
    @Resource
    private RestApiHelper restApiHelper;

    @PostConstruct
    public void init() {
        log.info("【Bing配置】配置属性对象: {}", JSON.toJSONString(bingPromotionConfigProperties));
        log.info("【Bing配置】reportDataCustomerId: {}", bingPromotionConfigProperties.getReportDataCustomerId());
        log.info("【Bing配置】limit: {}", bingPromotionConfigProperties.getLimit());

        List<ApplicationBO> applications = bingPromotionConfigProperties.getAppConfig();
        log.info("【Bing配置】应用配置列表: {}", JSON.toJSONString(applications));

        if (applications != null && !applications.isEmpty()) {
            appConfigMap = applications.stream().collect(Collectors.toMap(ApplicationBO::getCustomerId, value -> value));
            log.info("【Bing配置】应用配置Map keys: {}", appConfigMap.keySet());
        } else {
            log.warn("【Bing配置】应用配置为空，请检查配置文件");
        }
    }

    /**
     * 获取当前应用程序的类型。
     */
    @Override
    public String getAppType() {
        return BING.getCode();
    }

    /**
     * 获取 Redis 辅助工具实例。
     */
    @Override
    public RedisHelper getRedisHelper() {
        return redisHelper;
    }

    /**
     * 处理Bing OAuth回调
     * @param param OAuth回调参数
     * @return JSON格式的响应字符串
     */
    public String callback(BingCallbackBO param) {
        log.info("Bing OAuth回调信息: {}", JSON.toJSONString(param));

        // 检查是否有错误
        if (param.hasError()) {
            log.error("Bing OAuth授权失败: {} - {}", param.getError(), param.getError_description());
            return getResponseJson(600011, "授权失败: " + param.getError_description(), null);
        }

        // 检查必要参数
        if (!param.isSuccess()) {
            log.error("Bing OAuth回调参数不完整: {}", JSON.toJSONString(param));
            return getResponseJson(600011, "回调参数不完整", null);
        }

        // 验证state并解析customerId
        String customerId = parseCustomerIdFromState(param.getState());
        if (StringUtils.isBlank(customerId)) {
            log.error("Bing OAuth state验证失败或无法解析customerId: {}", param.getState());
            return getResponseJson(600011, "状态验证失败", null);
        }

        // 获取应用配置
        ApplicationBO application = getAppByCustomerId(customerId);
        if (application == null) {
            log.error("未找到customerId对应的应用配置: {}", customerId);
            return getResponseJson(600011, "应用配置不存在", null);
        }

        // 使用授权码换取访问令牌
        String accessToken = doAccessToken(param.getCode(), application);
        if (StringUtils.isBlank(accessToken)) {
            return getResponseJson(600011, "未获取到 access_token", null);
        }

        return getResponseJson(0, "success", accessToken);
    }

    /**
     * 使用授权码换取访问令牌
     */
    private String doAccessToken(String authCode, ApplicationBO application) {
        try {
            log.info("【Bing OAuth】开始换取访问令牌，customerId: {}", application.getCustomerId());

            // 构建请求参数
            Map<String, String> params = new HashMap<>();
            params.put("grant_type", "authorization_code");
            params.put("client_id", application.getClientId());
            params.put("client_secret", application.getClientSecret());
            params.put("code", authCode);
            params.put("redirect_uri", bingPromotionConfigProperties.getRedirectUri());
            params.put("scope", "https://ads.microsoft.com/msads.manage offline_access");

            // 发送请求
            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/x-www-form-urlencoded");

            String formData = params.entrySet().stream()
                    .map(entry -> entry.getKey() + "=" + entry.getValue())
                    .collect(Collectors.joining("&"));

            String responseJson = restApiHelper.post(OAUTH_TOKEN_URL, formData, headers);
            log.info("【Bing OAuth】换取访问令牌响应: {}", responseJson);

            // 解析响应
            JSONObject response = JSON.parseObject(responseJson);
            if (response.containsKey("error")) {
                log.error("【Bing OAuth】换取访问令牌失败: {}", response.getString("error_description"));
                return null;
            }

            // 保存token到Redis
            AccountTokenBO accountToken = saveAccountTokenInRedis(response, application.getCustomerId());
            return accountToken.getAccessToken();

        } catch (Exception e) {
            log.error("【Bing OAuth】换取访问令牌异常", e);
            return null;
        }
    }

    /**
     * 保存账户令牌到Redis
     */
    private AccountTokenBO saveAccountTokenInRedis(JSONObject tokenResponse, String customerId) {
        // 计算过期时间
        int expiresIn = tokenResponse.getIntValue("expires_in"); // 通常是3600秒
        Date expiresTime = DateTimeUtil.DateAddSecond(expiresIn - 300); // 提前5分钟过期

        // Bing的refresh_token通常有效期很长，设置为90天
        Date refreshExpiresTime = DateTimeUtil.DateAddDayOfYear(90);

        AccountTokenBO accountToken = AccountTokenBO.builder()
                .accessToken(tokenResponse.getString("access_token"))
                .refreshToken(tokenResponse.getString("refresh_token"))
                .expiresTime(expiresTime)
                .refreshExpiresTime(refreshExpiresTime)
                .userId(customerId)
                .openId(customerId)
                .build();

        log.info("redisToken:{}",REDIS_KEY_TOKEN);
        redisHelper.hset(REDIS_KEY_TOKEN, customerId, JSON.toJSONString(accountToken));
        String redis = (String)redisHelper.hget(REDIS_KEY_TOKEN,customerId);
        log.info("redis:{}",redis);
        log.info("【Bing OAuth】Token保存成功，customerId: {}", customerId);

        return accountToken;
    }

    /**
     * 从state中解析customerId
     * state格式: bing_{customerId}_{timestamp}
     */
    private String parseCustomerIdFromState(String state) {
        if (StringUtils.isBlank(state)) {
            return null;
        }

        try {
            String[] parts = state.split("_");
            if (parts.length >= 3 && "bing".equals(parts[0])) {
                return parts[1];
            }
        } catch (Exception e) {
            log.warn("解析state失败: {}", state, e);
        }

        return null;
    }

    /**
     * 生成OAuth授权URL
     */
    public String generateAuthUrl(String customerId) {
        ApplicationBO application = getAppByCustomerId(customerId);
        if (application == null) {
            log.error("未找到customerId对应的应用配置: {}", customerId);
            return null;
        }

        // 生成state: bing_{customerId}_{timestamp}
        String state = "bing_" + customerId + "_" + System.currentTimeMillis();

        // 构建授权URL
        String authUrl = "https://login.microsoftonline.com/common/oauth2/v2.0/authorize" +
                "?client_id=" + application.getClientId() +
                "&response_type=code" +
                "&redirect_uri=" + bingPromotionConfigProperties.getRedirectUri() +
                "&scope=https://ads.microsoft.com/msads.manage offline_access" +
                "&state=" + state;

        log.info("【Bing OAuth】生成授权URL，customerId: {}, authUrl: {}", customerId, authUrl);
        return authUrl;
    }

    /**
     * 生成响应JSON
     */
    private String getResponseJson(int code, String message, String data) {
        JSONObject response = new JSONObject();
        response.put("code", code);
        response.put("message", message);
        response.put("data", data);
        return response.toJSONString();
    }

    /**
     * 刷新指定应用程序ID的访问令牌。
     */
    @Override
    public void doRefreshToken(String customerId) {
        log.info("【Bing广告】刷新accessToken：{}", customerId);

        log.info("customerId:{}",customerId);
        // 从redis中获取缓存数据
        String accountTokenJson = (String) getRedisHelper().hget(REDIS_KEY_TOKEN, customerId);
        log.info("redisToken:{}|{}",REDIS_KEY_TOKEN, accountTokenJson);
        if (StringUtils.isBlank(accountTokenJson)) {
            log.error("Bing广告刷新accessToken需要有生效状态的refreshToken字段1，当前不存在，请重新授权, customerId：{}", customerId);
            return;
        }

        AccountTokenBO accountToken = JSON.parseObject(accountTokenJson, AccountTokenBO.class);
        if (accountToken.getRefreshExpiresTime().before(new Date())) {
            log.error("Bing广告刷新accessToken需要有生效状态的refreshToken字段2，当前已失效，请重新授权, customerId：{}", customerId);
            return;
        }

        // 查询该customerId的应用信息
        ApplicationBO application = getAppByCustomerId(customerId);
        if (application == null) {
            log.error("Bing广告刷新accessToken失败，未找到customerId对应的应用配置: {}", customerId);
            return;
        }

        try {
            // 构建刷新令牌请求参数
            Map<String, String> params = new HashMap<>();
            params.put("grant_type", "refresh_token");
            params.put("client_id", application.getClientId());
            params.put("client_secret", application.getClientSecret());
            params.put("refresh_token", accountToken.getRefreshToken());
            params.put("scope", "https://ads.microsoft.com/msads.manage offline_access");

            // 发送请求
            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/x-www-form-urlencoded");

            String formData = params.entrySet().stream()
                    .map(entry -> entry.getKey() + "=" + entry.getValue())
                    .collect(Collectors.joining("&"));

            String responseJson = restApiHelper.post(OAUTH_TOKEN_URL, formData, headers);
            log.info("【Bing广告】刷新accessToken响应: {}", responseJson);

            // 解析响应
            JSONObject response = JSON.parseObject(responseJson);
            if (response.containsKey("error")) {
                log.error("【Bing广告】刷新accessToken失败: {}", response.getString("error_description"));
                return;
            }

            // 保存新的token到Redis
            saveAccountTokenInRedis(response, customerId);
            log.info("【Bing广告】刷新accessToken成功：{}", customerId);

        } catch (Exception e) {
            log.error("【Bing广告】刷新accessToken异常", e);
        }
    }

    /**
     * 查找搜索日志记录（使用 RestApiHelper）
     */
    @Override
    public RspList<RpSearchLog> findRpSearchLog(Date startDate, Date endDate, PromotionTypeEnum promotionType, int page) {
        return findRpSearchLogWithRestApi(startDate, endDate, promotionType, page);
    }

    /**
     * 查找搜索日志记录（使用 RestApiHelper）
     */
    public RspList<RpSearchLog> findRpSearchLogWithRestApi(Date startDate, Date endDate, PromotionTypeEnum promotionType, int page) {
        log.info("【Bing广告】使用 RestApiHelper 查找搜索日志记录：startDate-{}, endDate-{}, promotionType-{}, page-{}",
                startDate, endDate, promotionType, page);

        if (page < 1) {
            page = 1;
        }

        // 检查配置
        if (bingPromotionConfigProperties == null) {
            log.error("Bing推广配置为空");
            return BaseRspUtils.createErrorRspList("Bing推广配置为空");
        }

        String reportDataCustomerId = bingPromotionConfigProperties.getReportDataCustomerId();
        if (StringUtils.isBlank(reportDataCustomerId)) {
            log.error("Bing报告数据客户ID为空");
            return BaseRspUtils.createErrorRspList("Bing报告数据客户ID为空");
        }

        ApplicationBO application = getAppByCustomerId(reportDataCustomerId);
        if (application == null) {
            log.error("Bing广告查找搜索日志记录失败，未找到customerId对应的应用配置: {}", reportDataCustomerId);
            return BaseRspUtils.createErrorRspList("应用配置不存在，customerId: " + reportDataCustomerId);
        }

        // 获取访问令牌
        String accessToken = getAccessToken(reportDataCustomerId);
        if (StringUtils.isBlank(accessToken)) {
            log.error("获取访问令牌失败，customerId: {}", reportDataCustomerId);
            return BaseRspUtils.createErrorRspList("获取访问令牌失败");
        }

        // 确定设备过滤器
        String deviceFilter = null;
        if (promotionType != null && !ALL_PROMOTION_TYPE.equals(promotionType)) {
            deviceFilter = promotionType.getBingType();
        }

        // 使用 RestApiHelper 提交报告请求
        String reportRequestId = BingReportUtils.submitReportRequestWithRestApi(
            restApiHelper,
            application.getCustomerId(),
            application.getCustomerAccountId(),
            application.getDeveloperToken(),
            accessToken,
            startDate,
            endDate,
            deviceFilter
        );

        if (StringUtils.isBlank(reportRequestId)) {
            log.error("【Bing广告】提交报告请求失败");
            return BaseRspUtils.createErrorRspList("提交报告请求失败");
        }

        log.info("【Bing广告】成功获取报告请求ID: {}", reportRequestId);

        // 轮询报告状态并下载数据
        return pollAndDownloadReportWithRestApi(application, accessToken, reportRequestId, promotionType);
    }

    /**
     * 使用 RestApiHelper 轮询报告状态并下载数据
     */
    private RspList<RpSearchLog> pollAndDownloadReportWithRestApi(ApplicationBO application, String accessToken,
                                                                 String reportRequestId, PromotionTypeEnum promotionType) {
        try {
            log.info("【Bing广告】使用 RestApiHelper 开始轮询报告状态，reportRequestId: {}", reportRequestId);

            // 轮询报告状态，最多等待5分钟
            int maxAttempts = 30; // 每10秒轮询一次，最多30次
            int attempt = 0;

            while (attempt < maxAttempts) {
                Thread.sleep(10000); // 等待10秒
                attempt++;

                // 使用 RestApiHelper 轮询状态
                BingReportUtils.ReportStatus status = BingReportUtils.pollReportStatusWithRestApi(
                    restApiHelper,
                    application.getCustomerId(),
                    application.getCustomerAccountId(),
                    application.getDeveloperToken(),
                    accessToken,
                    reportRequestId
                );

                if (status == null) {
                    log.error("【Bing广告】轮询报告状态失败 (第{}次)", attempt);
                    if (attempt >= maxAttempts) {
                        return BaseRspUtils.createErrorRspList("轮询报告状态失败");
                    }
                    continue;
                }

                log.info("【Bing广告】报告状态: {}, 下载URL: {}", status.getStatus(), status.getDownloadUrl());

                if (status.isSuccess() && StringUtils.isNotBlank(status.getDownloadUrl())) {
                    // 报告生成成功，下载数据
                    log.info("【Bing广告】报告生成成功，开始下载: {}", status.getDownloadUrl());
                    return downloadAndParseReport(status.getDownloadUrl(), promotionType);
                } else if (status.isError()) {
                    log.error("【Bing广告】报告生成失败，状态: {}", status.getStatus());
                    return BaseRspUtils.createErrorRspList("报告生成失败");
                } else if (status.isPending()) {
                    log.info("【Bing广告】报告正在生成中，继续等待... (第{}次)", attempt);
                    continue;
                } else {
                    log.warn("【Bing广告】未知的报告状态: {}", status.getStatus());
                }
            }

            log.error("【Bing广告】报告生成超时，已尝试{}次", maxAttempts);
            return BaseRspUtils.createErrorRspList("报告生成超时");

        } catch (Exception e) {
            log.error("【Bing广告】轮询报告状态失败", e);
            return BaseRspUtils.createErrorRspList("轮询报告状态失败: " + e.getMessage());
        }
    }

    /**
     * 下载并解析报告数据
     */
    private RspList<RpSearchLog> downloadAndParseReport(String downloadUrl, PromotionTypeEnum promotionType) {
        try {
            log.info("【Bing广告】开始下载报告数据，URL: {}", downloadUrl);

            List<String[]> csvData = BingReportUtils.downloadCsvReport(restApiHelper, downloadUrl);
            log.info("【Bing广告】成功下载CSV数据，行数: {}", csvData.size());

            List<RpSearchLog> rpSearchLogs = new ArrayList<>();

            for (int i = 0; i < csvData.size(); i++) {
                String[] row = csvData.get(i);
                try {
                    RpSearchLog rpSearchLog = parseCsvRow(row, promotionType);
                    if (rpSearchLog != null) {
                        rpSearchLogs.add(rpSearchLog);
                    }
                } catch (Exception e) {
                    log.warn("【Bing广告】解析CSV第{}行失败: {}", i + 1, Arrays.toString(row), e);
                }
            }

            log.info("【Bing广告】成功解析搜索日志记录数: {}", rpSearchLogs.size());
            return BaseRspUtils.createSuccessRspList(rpSearchLogs, (long) rpSearchLogs.size());

        } catch (Exception e) {
            log.error("【Bing广告】下载并解析报告数据失败", e);
            return BaseRspUtils.createErrorRspList("下载并解析报告数据失败: " + e.getMessage());
        }
    }

    /**
     * 解析CSV行数据
     */
    private RpSearchLog parseCsvRow(String[] row, PromotionTypeEnum promotionType) {
        try {
            if (row.length < 12) {
                log.warn("CSV行数据不完整: {}", Arrays.toString(row));
                return null;
            }

            RpSearchLog rpSearchLog = new RpSearchLog();

            // CSV列顺序: TimePeriod, SearchQuery, CampaignName, AdGroupName, Keyword,
            //           Impressions, Clicks, Spend, Ctr, AverageCpc, DeviceType, MatchType

            // 设置日期
            String timePeriod = row[0];
            if (StringUtils.isNotBlank(timePeriod)) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    rpSearchLog.setDayData(sdf.parse(timePeriod));
                } catch (Exception e) {
                    log.warn("解析日期失败: {}", timePeriod);
                    rpSearchLog.setDayData(new Date());
                }
            } else {
                rpSearchLog.setDayData(new Date());
            }

            // 设置来源
            if (promotionType != null && !PromotionTypeEnum.ALL_PROMOTION_TYPE.equals(promotionType)) {
                rpSearchLog.setSources("Bing".concat(promotionType.getDesc()));
            } else {
                String deviceType = row[10]; // DeviceType列
                if ("Computer".equals(deviceType)) {
                    rpSearchLog.setSources("BingPC端");
                } else if ("Smartphone".equals(deviceType) || "Tablet".equals(deviceType)) {
                    rpSearchLog.setSources("Bing移动端");
                } else {
                    rpSearchLog.setSources("Bing");
                }
            }

            // 设置其他字段
            rpSearchLog.setTerm(row[1]); // SearchQuery
            rpSearchLog.setPlanScheme(row[2]); // CampaignName
            rpSearchLog.setKeywordGroups(row[3]); // AdGroupName
            rpSearchLog.setKeyword(row[4]); // Keyword

            // 数值字段
            rpSearchLog.setImpressions(parseIntValue(row[5])); // Impressions
            rpSearchLog.setClick(parseIntValue(row[6])); // Clicks

            // 消费 (美元转分)
            BigDecimal spend = parseBigDecimalValue(row[7]);
            rpSearchLog.setConsumption(spend.multiply(BigDecimal.valueOf(100)).intValue());

            // 点击率 (百分比转万分比)
            BigDecimal ctr = parseBigDecimalValue(row[8].replace("%", ""));
            rpSearchLog.setClickRate(ctr.multiply(BigDecimal.valueOf(100)).intValue());

            // 平均点击价格 (美元转分)
            BigDecimal avgCpc = parseBigDecimalValue(row[9]);
            rpSearchLog.setAvgClickPrice(avgCpc.multiply(BigDecimal.valueOf(100)).intValue());

            // 匹配模式
            rpSearchLog.setMatchMode(row[11]); // MatchType

            return rpSearchLog;

        } catch (Exception e) {
            log.error("解析CSV行数据失败: {}", Arrays.toString(row), e);
            return null;
        }
    }

    /**
     * 解析整数值
     */
    private int parseIntValue(String value) {
        if (StringUtils.isBlank(value) || "--".equals(value)) {
            return 0;
        }
        try {
            return Integer.parseInt(value.replace(",", ""));
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    /**
     * 解析BigDecimal值
     */
    private BigDecimal parseBigDecimalValue(String value) {
        if (StringUtils.isBlank(value) || "--".equals(value)) {
            return BigDecimal.ZERO;
        }
        try {
            return new BigDecimal(value.replace(",", ""));
        } catch (NumberFormatException e) {
            return BigDecimal.ZERO;
        }
    }

    /**
     * 根据customerId获取应用配置（公开方法，用于测试）
     */
    public ApplicationBO getAppByCustomerId(String customerId) {
        return appConfigMap.get(customerId);
    }

    /**
     * 测试RestApiHelper提交报告请求（用于测试）
     */
    public String testSubmitReportRequestWithRestApi(String customerId, Date startDate, Date endDate, String deviceFilter) {
        log.info("【Bing广告】测试RestApiHelper提交报告请求，customerId: {}", customerId);

        ApplicationBO application = getAppByCustomerId(customerId);
        if (application == null) {
            log.error("未找到customerId对应的应用配置: {}", customerId);
            return null;
        }

        // 获取访问令牌
        String accessToken = getAccessToken(customerId);
        if (StringUtils.isBlank(accessToken)) {
            log.error("获取访问令牌失败，customerId: {}", customerId);
            return null;
        }

        // 使用 RestApiHelper 提交报告请求
        return BingReportUtils.submitReportRequestWithRestApi(
            restApiHelper,
            application.getCustomerId(),
            application.getCustomerAccountId(),
            application.getDeveloperToken(),
            accessToken,
            startDate,
            endDate,
            deviceFilter
        );
    }

    /**
     * 测试RestApiHelper轮询报告状态（用于测试）
     */
    public BingReportUtils.ReportStatus testPollReportStatusWithRestApi(String customerId, String reportRequestId) {
        log.info("【Bing广告】测试RestApiHelper轮询报告状态，customerId: {}, reportRequestId: {}", customerId, reportRequestId);

        ApplicationBO application = getAppByCustomerId(customerId);
        if (application == null) {
            log.error("未找到customerId对应的应用配置: {}", customerId);
            return null;
        }

        // 获取访问令牌
        String accessToken = getAccessToken(customerId);
        if (StringUtils.isBlank(accessToken)) {
            log.error("获取访问令牌失败，customerId: {}", customerId);
            return null;
        }

        // 使用 RestApiHelper 轮询报告状态
        return BingReportUtils.pollReportStatusWithRestApi(
            restApiHelper,
            application.getCustomerId(),
            application.getCustomerAccountId(),
            application.getDeveloperToken(),
            accessToken,
            reportRequestId
        );
    }
}
