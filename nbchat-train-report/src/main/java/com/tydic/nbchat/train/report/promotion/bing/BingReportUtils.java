package com.tydic.nbchat.train.report.promotion.bing;

import com.alibaba.fastjson.JSONObject;
import com.microsoft.bingads.*;
import com.microsoft.bingads.v13.reporting.*;
import com.microsoft.bingads.v13.reporting.SearchQueryPerformanceReportRequest;
import com.microsoft.bingads.v13.reporting.SearchQueryPerformanceReportColumn;
import com.microsoft.bingads.v13.reporting.ReportFormat;
import com.microsoft.bingads.v13.reporting.ReportAggregation;
import com.microsoft.bingads.v13.reporting.ReportTime;
import com.microsoft.bingads.v13.reporting.Date;
import com.microsoft.bingads.v13.reporting.AccountThroughAdGroupReportScope;
import com.microsoft.bingads.v13.reporting.SearchQueryPerformanceReportFilter;
import com.microsoft.bingads.v13.reporting.DeviceTypeReportFilter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.http.HttpHeaders;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/6/26
 * @description Bing广告报告工具类
 */
@Slf4j
public class BingReportUtils {

    /**
     * 使用 Bing Ads SDK 提交报告生成请求
     */
    public static String submitReportRequestWithSdk(String customerId, String customerAccountId,
                                                   String developerToken, String accessToken,
                                                   java.util.Date startDate, java.util.Date endDate, String deviceFilter) {
        try {
            log.info("【Bing工具】使用 SDK 提交报告请求，customerId: {}, customerAccountId: {}", customerId, customerAccountId);

            // 创建授权数据
            AuthorizationData authorizationData = createAuthorizationData(customerId, customerAccountId, developerToken, accessToken);

            // 创建报告服务客户端
            ServiceClient<IReportingService> reportingService = new ServiceClient<>(
                authorizationData,
                ApiEnvironment.PRODUCTION,
                IReportingService.class
            );

            // 构建报告请求
            SearchQueryPerformanceReportRequest reportRequest = buildSearchQueryReportRequest(
                customerAccountId, startDate, endDate, deviceFilter);

            SubmitGenerateReportRequest submitRequest = new SubmitGenerateReportRequest();
            submitRequest.setReportRequest(reportRequest);

            // 提交报告请求
            SubmitGenerateReportResponse response = reportingService.getService().submitGenerateReport(submitRequest);
            String reportRequestId = response.getReportRequestId();

            log.info("【Bing工具】SDK 提交报告请求成功，reportRequestId: {}", reportRequestId);
            return reportRequestId;

        } catch (Exception e) {
            log.error("【Bing工具】SDK 提交报告请求异常", e);
            return null;
        }
    }

    /**
     * 创建授权数据
     */
    private static AuthorizationData createAuthorizationData(String customerId, String customerAccountId,
                                                           String developerToken, String accessToken) {
        AuthorizationData authorizationData = new AuthorizationData();
        authorizationData.setDeveloperToken(developerToken);
        authorizationData.setCustomerId(Long.parseLong(customerId));
        authorizationData.setAccountId(Long.parseLong(customerAccountId));


        Authentication authentication = new Authentication() {
            @Override
            public void addHeaders(HeadersImpl headers) {
                headers.addHeader("Authorization", "Bearer " + accessToken);
                headers.addHeader("CustomerAccountId", customerAccountId);
                headers.addHeader("CustomerId", customerId);
                headers.addHeader("DeveloperToken", developerToken);
                headers.addHeader("Content-Type", "application/json");
                headers.addHeader("Accept", "application/json");
            }
        };
        authorizationData.setAuthentication(authentication);

        return authorizationData;
    }

    /**
     * 构建搜索查询性能报告请求
     */
    private static SearchQueryPerformanceReportRequest buildSearchQueryReportRequest(String customerAccountId,
                                                                                   java.util.Date startDate,
                                                                                   java.util.Date endDate,
                                                                                   String deviceFilter) {
        SearchQueryPerformanceReportRequest reportRequest = new SearchQueryPerformanceReportRequest();

        // 基本设置
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        reportRequest.setReportName("SearchQueryPerformance_" + sdf.format(new java.util.Date()).replace("-", "_"));
        reportRequest.setFormat(ReportFormat.CSV);
        reportRequest.setReturnOnlyCompleteData(false);
        reportRequest.setAggregation(ReportAggregation.DAILY);

        // 时间范围
        ReportTime reportTime = new ReportTime();
        reportTime.setCustomDateRangeStart(convertToReportDate(startDate));
        reportTime.setCustomDateRangeEnd(convertToReportDate(endDate));
        reportRequest.setTime(reportTime);

        // 账户范围
        AccountThroughAdGroupReportScope scope = new AccountThroughAdGroupReportScope();
        ArrayOflong accountIds = new ArrayOflong();
        accountIds.getLongs().add(Long.parseLong(customerAccountId));
        scope.setAccountIds(accountIds);
        reportRequest.setScope(scope);

        // 列定义
        ArrayOfSearchQueryPerformanceReportColumn columns = new ArrayOfSearchQueryPerformanceReportColumn();
        columns.getSearchQueryPerformanceReportColumns().add(SearchQueryPerformanceReportColumn.TIME_PERIOD);
        columns.getSearchQueryPerformanceReportColumns().add(SearchQueryPerformanceReportColumn.SEARCH_QUERY);
        columns.getSearchQueryPerformanceReportColumns().add(SearchQueryPerformanceReportColumn.CAMPAIGN_NAME);
        columns.getSearchQueryPerformanceReportColumns().add(SearchQueryPerformanceReportColumn.AD_GROUP_NAME);
        columns.getSearchQueryPerformanceReportColumns().add(SearchQueryPerformanceReportColumn.KEYWORD);
        columns.getSearchQueryPerformanceReportColumns().add(SearchQueryPerformanceReportColumn.IMPRESSIONS);
        columns.getSearchQueryPerformanceReportColumns().add(SearchQueryPerformanceReportColumn.CLICKS);
        columns.getSearchQueryPerformanceReportColumns().add(SearchQueryPerformanceReportColumn.SPEND);
        columns.getSearchQueryPerformanceReportColumns().add(SearchQueryPerformanceReportColumn.CTR);
        columns.getSearchQueryPerformanceReportColumns().add(SearchQueryPerformanceReportColumn.AVERAGE_CPC);
        columns.getSearchQueryPerformanceReportColumns().add(SearchQueryPerformanceReportColumn.DEVICE_TYPE);
        columns.getSearchQueryPerformanceReportColumns().add(SearchQueryPerformanceReportColumn.DELIVERED_MATCH_TYPE);
        reportRequest.setColumns(columns);

//        // 设备过滤器
//        if (StringUtils.isNotBlank(deviceFilter)) {
//            SearchQueryPerformanceReportFilter filter = new SearchQueryPerformanceReportFilter();
//            filter.setDeviceType(DeviceTypeReportFilter.fromValue(deviceFilter.toUpperCase()));
//            reportRequest.setFilter(filter);
//        }

        return reportRequest;
    }

    /**
     * 转换 Java Date 到 Bing Ads SDK Date
     */
    private static Date convertToReportDate(java.util.Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        Date reportDate = new Date();
        reportDate.setYear(calendar.get(Calendar.YEAR));
        reportDate.setMonth(calendar.get(Calendar.MONTH) + 1); // Calendar.MONTH 是从0开始的
        reportDate.setDay(calendar.get(Calendar.DAY_OF_MONTH));

        return reportDate;
    }

    /**
     * 构建报告请求JSON（已废弃，保留用于兼容性）
     */
    @Deprecated
    private static JSONObject buildReportRequestJson(String customerId, String customerAccountId,
                                                     Date startDate, Date endDate, String deviceFilter) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        JSONObject reportRequest = new JSONObject();
        reportRequest.put("Type", "SearchQueryPerformanceReportRequest");
        reportRequest.put("ReportName", "SearchQueryPerformance_" + sdf.format(new Date()).replace("-", "_"));
        reportRequest.put("Format", "Csv");
        reportRequest.put("Aggregation", "Daily");
        reportRequest.put("ReturnOnlyCompleteData", false);
        reportRequest.put("ExcludeReportHeader", true);
        reportRequest.put("ExcludeReportFooter", true);
        reportRequest.put("ExcludeColumnHeaders", false);

        // 时间范围
        JSONObject time = new JSONObject();
        JSONObject startDateObj = new JSONObject();
        String[] startParts = sdf.format(startDate).split("-");
        startDateObj.put("Year", Integer.parseInt(startParts[0]));
        startDateObj.put("Month", Integer.parseInt(startParts[1]));
        startDateObj.put("Day", Integer.parseInt(startParts[2]));

        JSONObject endDateObj = new JSONObject();
        String[] endParts = sdf.format(endDate).split("-");
        endDateObj.put("Year", Integer.parseInt(endParts[0]));
        endDateObj.put("Month", Integer.parseInt(endParts[1]));
        endDateObj.put("Day", Integer.parseInt(endParts[2]));

        time.put("CustomDateRangeStart", startDateObj);
        time.put("CustomDateRangeEnd", endDateObj);
        reportRequest.put("Time", time);

        // 账户范围
        if (StringUtils.isNotBlank(customerAccountId)) {
            JSONObject scope = new JSONObject();
            List<Long> accountIds = new ArrayList<>();
            accountIds.add(Long.parseLong(customerAccountId));
            scope.put("AccountIds", accountIds);
            reportRequest.put("Scope", scope);
        }

        // 列定义
        List<String> columns = Arrays.asList(
            "TimePeriod", "SearchQuery", "CampaignName", "AdGroupName",
            "Keyword", "Impressions", "Clicks", "Spend", "Ctr",
            "AverageCpc", "DeviceType", "DeliveredMatchType"
        );
        reportRequest.put("Columns", columns);

        // 过滤器
        if (StringUtils.isNotBlank(deviceFilter)) {
            JSONObject filter = new JSONObject();
            filter.put("DeviceType", deviceFilter);
            reportRequest.put("Filter", filter);
        }

        return reportRequest;
    }



    /**
     * 使用REST API轮询报告状态
     */
    public static ReportStatus pollReportStatusWithSdk(String customerId, String customerAccountId,
                                                      String developerToken, String accessToken,
                                                      String reportRequestId) {
        try {
            log.info("【Bing工具】使用 SDK 轮询报告状态，reportRequestId: {}", reportRequestId);

            // 创建授权数据
            AuthorizationData authorizationData = createAuthorizationData(customerId, customerAccountId, developerToken, accessToken);

            // 创建报告服务客户端
            ServiceClient<IReportingService> reportingService = new ServiceClient<>(
                authorizationData,
                ApiEnvironment.PRODUCTION,
                IReportingService.class
            );

            PollGenerateReportRequest pollRequest = new PollGenerateReportRequest();
            pollRequest.setReportRequestId(reportRequestId);

            // 轮询报告状态
            PollGenerateReportResponse response = reportingService.getService().pollGenerateReport(pollRequest);

            ReportStatus status = new ReportStatus();
            status.setStatus(response.getReportRequestStatus().toString());

            if (response.getReportRequestStatus().getReportDownloadUrl() != null) {
                status.setDownloadUrl(response.getReportRequestStatus().getReportDownloadUrl());
            }

            log.info("【Bing工具】SDK 报告状态: {}, 下载URL: {}", status.getStatus(), status.getDownloadUrl());
            return status;

        } catch (Exception e) {
            log.error("【Bing工具】SDK 轮询报告状态异常", e);
            return null;
        }
    }





    /**
     * 下载CSV报告数据
     */
    public static List<String[]> downloadCsvReport(String downloadUrl) {
        List<String[]> csvData = new ArrayList<>();

        try {
            log.info("【Bing工具】开始下载CSV报告，URL: {}", downloadUrl);

            URL url = new URL(downloadUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(30000); // 30秒连接超时
            connection.setReadTimeout(60000); // 60秒读取超时
            connection.setRequestProperty("User-Agent", "NBChat-BingAds-Client/1.0");

            int responseCode = connection.getResponseCode();
            log.info("【Bing工具】CSV下载响应码: {}", responseCode);

            if (responseCode != 200) {
                log.error("【Bing工具】CSV下载失败，响应码: {}", responseCode);
                return csvData;
            }

            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String line;
            boolean isFirstLine = true;
            int lineCount = 0;

            while ((line = reader.readLine()) != null) {
                lineCount++;

                // 跳过标题行
                if (isFirstLine) {
                    isFirstLine = false;
                    log.info("【Bing工具】CSV标题行: {}", line);
                    continue;
                }

                // 解析CSV行
                String[] fields = parseCsvLine(line);
                if (fields != null && fields.length > 0) {
                    csvData.add(fields);
                }
            }

            reader.close();
            connection.disconnect();

            log.info("【Bing工具】CSV下载完成，总行数: {}，数据行数: {}", lineCount, csvData.size());

        } catch (Exception e) {
            log.error("【Bing工具】下载CSV报告失败，URL: " + downloadUrl, e);
        }

        return csvData;
    }

    /**
     * 解析CSV行，处理引号和逗号
     */
    private static String[] parseCsvLine(String line) {
        if (StringUtils.isBlank(line)) {
            return null;
        }
        
        List<String> fields = new ArrayList<>();
        StringBuilder currentField = new StringBuilder();
        boolean inQuotes = false;
        
        for (int i = 0; i < line.length(); i++) {
            char c = line.charAt(i);
            
            if (c == '"') {
                inQuotes = !inQuotes;
            } else if (c == ',' && !inQuotes) {
                fields.add(currentField.toString().trim());
                currentField = new StringBuilder();
            } else {
                currentField.append(c);
            }
        }
        
        // 添加最后一个字段
        fields.add(currentField.toString().trim());
        
        return fields.toArray(new String[0]);
    }

    /**
     * 报告状态类
     */
    public static class ReportStatus {
        private String status;
        private String downloadUrl;

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getDownloadUrl() {
            return downloadUrl;
        }

        public void setDownloadUrl(String downloadUrl) {
            this.downloadUrl = downloadUrl;
        }

        public boolean isSuccess() {
            return "Success".equals(status);
        }

        public boolean isPending() {
            return "Pending".equals(status);
        }

        public boolean isError() {
            return "Error".equals(status);
        }
    }
}
