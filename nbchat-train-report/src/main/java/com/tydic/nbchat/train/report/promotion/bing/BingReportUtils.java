package com.tydic.nbchat.train.report.promotion.bing;

import com.alibaba.easyexcel.EasyExcel;
import com.alibaba.easyexcel.context.AnalysisContext;
import com.alibaba.easyexcel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tydic.nicc.dc.boot.starter.http.RestApiHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;

import java.io.ByteArrayInputStream;
import java.io.StringReader;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/6/26
 * @description Bing广告报告工具类 - 使用 RestApiHelper
 */
@Slf4j
public class BingReportUtils {

    private static final String BING_REPORTING_API_URL = "https://reporting.api.bingads.microsoft.com/Reporting/v13/GenerateReport/Submit";
    private static final String BING_POLL_API_URL = "https://reporting.api.bingads.microsoft.com/Reporting/v13/GenerateReport/Poll";

    /**
     * 使用 RestApiHelper 提交报告生成请求
     */
    public static String submitReportRequestWithRestApi(RestApiHelper restApiHelper, String customerId, String customerAccountId,
                                                       String developerToken, String accessToken,
                                                       java.util.Date startDate, java.util.Date endDate, String deviceFilter) {
        try {
            log.info("【Bing工具】使用 RestApiHelper 提交报告请求，customerId: {}, customerAccountId: {}", customerId, customerAccountId);

            // 构建请求体
            JSONObject reportRequest = buildReportRequestJson(customerId, customerAccountId, startDate, endDate, deviceFilter);
            
            // 构建完整请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("ReportRequest", reportRequest);
            
            String requestJson = requestBody.toJSONString();
            log.debug("【Bing工具】请求体: {}", requestJson);

            // 构建请求头
            HttpHeaders headers = buildRequestHeaders(customerId, customerAccountId, developerToken, accessToken);

            // 使用 restApiHelper 发送请求
            String responseBody = restApiHelper.post(BING_REPORTING_API_URL, requestJson, headers);
            log.info("【Bing工具】提交报告请求响应: {}", responseBody);

            // 提取并解析 JSON 字符串
            String jsonString = extractJsonFromResponse(responseBody);
            if (StringUtils.isBlank(jsonString)) {
                log.error("【Bing工具】无法从响应中提取有效的 JSON 字符串");
                return null;
            }

            // 解析 JSON 获取报告请求ID
            JSONObject responseJson = JSON.parseObject(jsonString);
            String reportRequestId = responseJson.getString("ReportRequestId");
            
            log.info("【Bing工具】成功提取报告请求ID: {}", reportRequestId);
            return reportRequestId;

        } catch (Exception e) {
            log.error("【Bing工具】提交报告请求异常", e);
            return null;
        }
    }

    /**
     * 使用 RestApiHelper 轮询报告状态
     */
    public static ReportStatus pollReportStatusWithRestApi(RestApiHelper restApiHelper, String customerId, String customerAccountId,
                                                          String developerToken, String accessToken,
                                                          String reportRequestId) {
        try {
            log.info("【Bing工具】使用 RestApiHelper 轮询报告状态，reportRequestId: {}", reportRequestId);

            // 构建轮询URL
//            String pollUrl = BING_POLL_API_URL + "?ReportRequestId=" + reportRequestId;

            // 构建请求头
            HttpHeaders headers = buildRequestHeaders(customerId, customerAccountId, developerToken, accessToken);

            // 构建完整请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("ReportRequestId", reportRequestId);

            String requestJson = requestBody.toJSONString();

            // 使用 restApiHelper 发送GET请求
            String responseBody = restApiHelper.post(BING_POLL_API_URL, requestJson,headers);
            log.debug("【Bing工具】轮询报告状态响应: {}", responseBody);

            // 提取并解析 JSON 字符串
            String jsonString = extractJsonFromResponse(responseBody);
            if (StringUtils.isBlank(jsonString)) {
                log.error("【Bing工具】无法从轮询响应中提取有效的 JSON 字符串");
                return null;
            }

            // 解析响应

            // jsonString 是 Poll 接口返回的完整字符串
            JSONObject responseJson = JSON.parseObject(jsonString);

// 先取出 "ReportRequestStatus" 这一层
            JSONObject reportStatus = responseJson.getJSONObject("ReportRequestStatus");

            ReportStatus status = new ReportStatus();
            status.setStatus(reportStatus.getString("Status"));
            status.setDownloadUrl(reportStatus.getString("ReportDownloadUrl"));

            log.info("【Bing工具】报告状态: {}, 下载URL: {}", status.getStatus(), status.getDownloadUrl());
            return status;

        } catch (Exception e) {
            log.error("【Bing工具】轮询报告状态异常", e);
            return null;
        }
    }

    /**
     * 从响应中提取 JSON 字符串
     * 处理可能的响应格式：纯JSON、包含其他内容的响应等
     */
    private static String extractJsonFromResponse(String responseBody) {
        if (StringUtils.isBlank(responseBody)) {
            return null;
        }

        try {
            // 去除前后空白字符
            String trimmed = responseBody.trim();
            
            // 如果响应直接是 JSON 格式，直接返回
            if (trimmed.startsWith("{") && trimmed.endsWith("}")) {
                log.debug("【Bing工具】响应是纯 JSON 格式");
                return trimmed;
            }
            
            // 如果响应包含其他内容，尝试提取 JSON 部分
            int jsonStart = trimmed.indexOf("{");
            int jsonEnd = trimmed.lastIndexOf("}");
            
            if (jsonStart >= 0 && jsonEnd > jsonStart) {
                String extractedJson = trimmed.substring(jsonStart, jsonEnd + 1);
                log.debug("【Bing工具】从响应中提取的 JSON: {}", extractedJson);
                
                // 验证提取的字符串是否为有效 JSON
                JSON.parseObject(extractedJson);
                return extractedJson;
            }
            
            log.warn("【Bing工具】无法从响应中找到有效的 JSON 格式: {}", responseBody);
            return null;
            
        } catch (Exception e) {
            log.error("【Bing工具】提取 JSON 字符串时发生异常，响应内容: {}", responseBody, e);
            return null;
        }
    }

    /**
     * 构建报告请求JSON
     */
    private static JSONObject buildReportRequestJson(String customerId, String customerAccountId,
                                                   java.util.Date startDate, java.util.Date endDate, String deviceFilter) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        
        JSONObject reportRequest = new JSONObject();
        reportRequest.put("Type", "SearchQueryPerformanceReportRequest");
        reportRequest.put("ReportName", "SearchQueryPerformance_" + sdf.format(new java.util.Date()).replace("-", "_"));
        reportRequest.put("Format", "Csv");
        reportRequest.put("Aggregation", "Daily");
        reportRequest.put("ReturnOnlyCompleteData", false);
        reportRequest.put("ExcludeReportHeader", true);
        reportRequest.put("ExcludeReportFooter", true);
        reportRequest.put("ExcludeColumnHeaders", false);

        // 时间范围
        JSONObject time = new JSONObject();
        JSONObject startDateObj = new JSONObject();
        String[] startParts = sdf.format(startDate).split("-");
        startDateObj.put("Year", Integer.parseInt(startParts[0]));
        startDateObj.put("Month", Integer.parseInt(startParts[1]));
        startDateObj.put("Day", Integer.parseInt(startParts[2]));
        
        JSONObject endDateObj = new JSONObject();
        String[] endParts = sdf.format(endDate).split("-");
        endDateObj.put("Year", Integer.parseInt(endParts[0]));
        endDateObj.put("Month", Integer.parseInt(endParts[1]));
        endDateObj.put("Day", Integer.parseInt(endParts[2]));
        
        time.put("CustomDateRangeStart", startDateObj);
        time.put("CustomDateRangeEnd", endDateObj);
        reportRequest.put("Time", time);

        // 账户范围
        if (StringUtils.isNotBlank(customerAccountId)) {
            JSONObject scope = new JSONObject();
            List<Long> accountIds = new ArrayList<>();
            accountIds.add(Long.parseLong(customerAccountId));
            scope.put("AccountIds", accountIds);
            reportRequest.put("Scope", scope);
        }

        // 列定义
        List<String> columns = Arrays.asList(
            "TimePeriod", "SearchQuery", "CampaignName", "AdGroupName",
            "Keyword", "Impressions", "Clicks", "Spend", "Ctr",
            "AverageCpc", "DeviceType", "DeliveredMatchType"
        );
        reportRequest.put("Columns", columns);

        // 过滤器
        if (StringUtils.isNotBlank(deviceFilter)) {
            JSONObject filter = new JSONObject();
            filter.put("DeviceType", deviceFilter);
            reportRequest.put("Filter", filter);
        }

        return reportRequest;
    }

    /**
     * 构建请求头
     */
    private static HttpHeaders buildRequestHeaders(String customerId, String customerAccountId, 
                                                  String developerToken, String accessToken) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Accept", "application/json");
        headers.set("Authorization", "Bearer " + accessToken);
        headers.set("Connection", "keep-alive");
        headers.set("Content-Type", "application/json");
        headers.set("CustomerAccountId", customerAccountId);
        headers.set("CustomerId", customerId);
        headers.set("DeveloperToken", developerToken);
        return headers;
    }

    /**
     * 下载CSV报告
     */
    public static List<String[]> downloadCsvReport(String downloadUrl) {
        List<String[]> csvData = new ArrayList<>();

        try {
            log.info("【Bing工具】开始下载CSV报告，URL: {}", downloadUrl);
            URL url = new URL(downloadUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(30000);
            connection.setReadTimeout(60000);

            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String line;

            while ((line = reader.readLine()) != null) {
                // 简单的CSV解析，假设字段用逗号分隔
                String[] fields = line.split(",");
                csvData.add(fields);
            }

            reader.close();
            log.info("【Bing工具】CSV报告下载完成，共{}行数据", csvData.size());

        } catch (Exception e) {
            log.error("【Bing工具】下载CSV报告失败", e);
        }

        return csvData;
    }

    /**
     * 报告状态类
     */
    public static class ReportStatus {
        private String status;
        private String downloadUrl;

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getDownloadUrl() {
            return downloadUrl;
        }

        public void setDownloadUrl(String downloadUrl) {
            this.downloadUrl = downloadUrl;
        }

        public boolean isSuccess() {
            return "Success".equalsIgnoreCase(status);
        }

        public boolean isPending() {
            return "Pending".equalsIgnoreCase(status) || "InProgress".equalsIgnoreCase(status);
        }

        public boolean isError() {
            return "Error".equalsIgnoreCase(status) || "Failed".equalsIgnoreCase(status);
        }
    }
}
