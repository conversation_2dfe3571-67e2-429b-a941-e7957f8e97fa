package com.tydic.nbchat.train.report.promotion.bing;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tydic.nicc.dc.boot.starter.http.RestApiHelper;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.net.URI;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/6/26
 * @description Bing广告报告工具类 - 使用 RestApiHelper
 */
@Slf4j
public class BingReportUtils {

    private static final String BING_REPORTING_API_URL = "https://reporting.api.bingads.microsoft.com/Reporting/v13/GenerateReport/Submit";
    private static final String BING_POLL_API_URL = "https://reporting.api.bingads.microsoft.com/Reporting/v13/GenerateReport/Poll";

    /**
     * 使用 RestApiHelper 提交报告生成请求
     */
    public static String submitReportRequestWithRestApi(RestApiHelper restApiHelper, String customerId, String customerAccountId,
                                                       String developerToken, String accessToken,
                                                       java.util.Date startDate, java.util.Date endDate, String deviceFilter) {
        try {
            log.info("【Bing工具】使用 RestApiHelper 提交报告请求，customerId: {}, customerAccountId: {}", customerId, customerAccountId);

            // 构建请求体
            JSONObject reportRequest = buildReportRequestJson(customerId, customerAccountId, startDate, endDate, deviceFilter);
            
            // 构建完整请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("ReportRequest", reportRequest);
            
            String requestJson = requestBody.toJSONString();
            log.debug("【Bing工具】请求体: {}", requestJson);

            // 构建请求头
            HttpHeaders headers = buildRequestHeaders(customerId, customerAccountId, developerToken, accessToken);

            // 使用 restApiHelper 发送请求
            String responseBody = restApiHelper.post(BING_REPORTING_API_URL, requestJson, headers);
            log.info("【Bing工具】提交报告请求响应: {}", responseBody);

            // 提取并解析 JSON 字符串
            String jsonString = extractJsonFromResponse(responseBody);
            if (StringUtils.isBlank(jsonString)) {
                log.error("【Bing工具】无法从响应中提取有效的 JSON 字符串");
                return null;
            }

            // 解析 JSON 获取报告请求ID
            JSONObject responseJson = JSON.parseObject(jsonString);
            String reportRequestId = responseJson.getString("ReportRequestId");
            
            log.info("【Bing工具】成功提取报告请求ID: {}", reportRequestId);
            return reportRequestId;

        } catch (Exception e) {
            log.error("【Bing工具】提交报告请求异常", e);
            return null;
        }
    }

    /**
     * 使用 RestApiHelper 轮询报告状态
     */
    public static ReportStatus pollReportStatusWithRestApi(RestApiHelper restApiHelper, String customerId, String customerAccountId,
                                                           String developerToken, String accessToken,
                                                           String reportRequestId) {
        try {
            log.info("【Bing工具】使用 RestApiHelper 轮询报告状态，reportRequestId: {}", reportRequestId);

            // 构建轮询URL
//            String pollUrl = BING_POLL_API_URL + "?ReportRequestId=" + reportRequestId;

            // 构建请求头
            HttpHeaders headers = buildRequestHeaders(customerId, customerAccountId, developerToken, accessToken);

            // 构建完整请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("ReportRequestId", reportRequestId);

            String requestJson = requestBody.toJSONString();

            // 使用 restApiHelper 发送GET请求
            String responseBody = restApiHelper.post(BING_POLL_API_URL, requestJson,headers);
            log.debug("【Bing工具】轮询报告状态响应: {}", responseBody);

            // 提取并解析 JSON 字符串
            String jsonString = extractJsonFromResponse(responseBody);
            if (StringUtils.isBlank(jsonString)) {
                log.error("【Bing工具】无法从轮询响应中提取有效的 JSON 字符串");
                return null;
            }

            // 解析响应

            // jsonString 是 Poll 接口返回的完整字符串
            JSONObject responseJson = JSON.parseObject(jsonString);

// 先取出 "ReportRequestStatus" 这一层
            JSONObject reportStatus = responseJson.getJSONObject("ReportRequestStatus");

            ReportStatus status = new ReportStatus();
            status.setStatus(reportStatus.getString("Status"));
            status.setDownloadUrl(reportStatus.getString("ReportDownloadUrl"));

            log.info("【Bing工具】报告状态: {}, 下载URL: {}", status.getStatus(), status.getDownloadUrl());
            return status;

        } catch (Exception e) {
            log.error("【Bing工具】轮询报告状态异常", e);
            return null;
        }
    }

    /**
     * 从响应中提取 JSON 字符串
     * 处理可能的响应格式：纯JSON、包含其他内容的响应等
     */
    private static String extractJsonFromResponse(String responseBody) {
        if (StringUtils.isBlank(responseBody)) {
            return null;
        }

        try {
            // 去除前后空白字符
            String trimmed = responseBody.trim();
            
            // 如果响应直接是 JSON 格式，直接返回
            if (trimmed.startsWith("{") && trimmed.endsWith("}")) {
                log.debug("【Bing工具】响应是纯 JSON 格式");
                return trimmed;
            }
            
            // 如果响应包含其他内容，尝试提取 JSON 部分
            int jsonStart = trimmed.indexOf("{");
            int jsonEnd = trimmed.lastIndexOf("}");
            
            if (jsonStart >= 0 && jsonEnd > jsonStart) {
                String extractedJson = trimmed.substring(jsonStart, jsonEnd + 1);
                log.debug("【Bing工具】从响应中提取的 JSON: {}", extractedJson);
                
                // 验证提取的字符串是否为有效 JSON
                JSON.parseObject(extractedJson);
                return extractedJson;
            }
            
            log.warn("【Bing工具】无法从响应中找到有效的 JSON 格式: {}", responseBody);
            return null;
            
        } catch (Exception e) {
            log.error("【Bing工具】提取 JSON 字符串时发生异常，响应内容: {}", responseBody, e);
            return null;
        }
    }

    /**
     * 构建报告请求JSON
     */
    private static JSONObject buildReportRequestJson(String customerId, String customerAccountId,
                                                   java.util.Date startDate, java.util.Date endDate, String deviceFilter) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        
        JSONObject reportRequest = new JSONObject();
        reportRequest.put("Type", "SearchQueryPerformanceReportRequest");
        reportRequest.put("ReportName", "SearchQueryPerformance_" + sdf.format(new java.util.Date()).replace("-", "_"));
        reportRequest.put("Format", "Csv");
        reportRequest.put("Aggregation", "Daily");
        reportRequest.put("ReturnOnlyCompleteData", false);
        reportRequest.put("ExcludeReportHeader", true);
        reportRequest.put("ExcludeReportFooter", true);
        reportRequest.put("ExcludeColumnHeaders", false);

        // 时间范围
        JSONObject time = new JSONObject();
        JSONObject startDateObj = new JSONObject();
        String[] startParts = sdf.format(startDate).split("-");
        startDateObj.put("Year", Integer.parseInt(startParts[0]));
        startDateObj.put("Month", Integer.parseInt(startParts[1]));
        startDateObj.put("Day", Integer.parseInt(startParts[2]));
        
        JSONObject endDateObj = new JSONObject();
        String[] endParts = sdf.format(endDate).split("-");
        endDateObj.put("Year", Integer.parseInt(endParts[0]));
        endDateObj.put("Month", Integer.parseInt(endParts[1]));
        endDateObj.put("Day", Integer.parseInt(endParts[2]));
        
        time.put("CustomDateRangeStart", startDateObj);
        time.put("CustomDateRangeEnd", endDateObj);
        reportRequest.put("Time", time);

        // 账户范围
        if (StringUtils.isNotBlank(customerAccountId)) {
            JSONObject scope = new JSONObject();
            List<Long> accountIds = new ArrayList<>();
            accountIds.add(Long.parseLong(customerAccountId));
            scope.put("AccountIds", accountIds);
            reportRequest.put("Scope", scope);
        }

        // 列定义
        List<String> columns = Arrays.asList(
            "TimePeriod", "SearchQuery", "CampaignName", "AdGroupName",
            "Keyword", "Impressions", "Clicks", "Spend", "Ctr",
            "AverageCpc", "DeviceType", "DeliveredMatchType"
        );
        reportRequest.put("Columns", columns);

        // 过滤器
        if (StringUtils.isNotBlank(deviceFilter)) {
            JSONObject filter = new JSONObject();
            filter.put("DeviceType", deviceFilter);
            reportRequest.put("Filter", filter);
        }

        return reportRequest;
    }

    /**
     * 构建请求头
     */
    private static HttpHeaders buildRequestHeaders(String customerId, String customerAccountId,
                                                   String developerToken, String accessToken) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Accept", "application/json");
        headers.set("Authorization", "Bearer " + accessToken);
        headers.set("Connection", "keep-alive");
        headers.set("Content-Type", "application/json");
        headers.set("CustomerAccountId", customerAccountId);
        headers.set("CustomerId", customerId);
        headers.set("DeveloperToken", developerToken);
        return headers;
    }

    /**
     * 下载CSV/Excel报告（处理压缩包，完全使用 Hutool）
     */
    public static List<String[]> downloadCsvReport(String downloadUrl) {
        List<String[]> reportData = new ArrayList<>();

        try {
            log.info("【Bing工具】开始下载报告，URL: {}", downloadUrl);

            // 完全使用 Hutool 下载二进制数据
            byte[] fileData = downloadBinaryFileWithHutool(downloadUrl);
            log.info("【Bing工具】报告下载完成，数据大小: {} bytes", fileData != null ? fileData.length : 0);

            if (fileData == null || fileData.length == 0) {
                log.warn("【Bing工具】下载的报告数据为空");
                return reportData;
            }

            // 判断是否为压缩包并处理
            if (isZipFile(downloadUrl, fileData)) {
                log.info("【Bing工具】检测到压缩包，开始解压");
                reportData = extractAndParseZipFile(fileData);
            } else {
                log.info("【Bing工具】检测到非压缩文件，直接解析");
                // 根据文件类型解析
                if (isExcelFile(downloadUrl)) {
                    reportData = parseExcelContent(fileData);
                } else {
                    String fileContent = new String(fileData, "UTF-8");
                    reportData = parseCsvContent(fileContent);
                }
            }

            log.info("【Bing工具】报告解析完成，共{}行数据", reportData.size());

        } catch (Exception e) {
            log.error("【Bing工具】下载报告失败", e);
        }

        return reportData;
    }

    /**
     * 下载二进制文件（完全使用 Hutool HTTP 工具）
     */
    private static byte[] downloadBinaryFileWithHutool(String downloadUrl) {
        try {
            log.info("【Bing工具】开始使用 Hutool 下载二进制文件: {}", downloadUrl);

            // 使用 Hutool 的 HttpUtil 直接下载字节数组
            byte[] data = HttpUtil.downloadBytes(downloadUrl);

            if (data != null && data.length > 0) {
                log.info("【Bing工具】Hutool 下载完成，数据大小: {} bytes", data.length);

                // 打印前几个字节用于调试
                if (data.length >= 10) {
                    StringBuilder hexStr = new StringBuilder();
                    for (int i = 0; i < Math.min(10, data.length); i++) {
                        hexStr.append(String.format("%02X ", data[i] & 0xFF));
                    }
                    log.info("【Bing工具】文件头字节: {}", hexStr.toString());
                }

                return data;
            } else {
                log.error("【Bing工具】Hutool 下载响应为空");
                return null;
            }

        } catch (Exception e) {
            log.error("【Bing工具】Hutool 下载二进制文件异常: {}", e.getMessage(), e);
            return null;
        }
    }



    /**
     * 判断是否为压缩文件
     */
    private static boolean isZipFile(String downloadUrl, byte[] fileData) {
        log.info("【Bing工具】检测文件类型，URL: {}, 数据大小: {} bytes", downloadUrl, fileData != null ? fileData.length : 0);

        // 检查 URL 中是否包含 zip 扩展名
        boolean urlIndicatesZip = downloadUrl != null && downloadUrl.toLowerCase().contains(".zip");
        log.info("【Bing工具】URL 指示 ZIP: {}", urlIndicatesZip);

        // 检查文件头是否为 ZIP 格式 (PK)
        boolean headerIndicatesZip = false;
        if (fileData != null && fileData.length >= 2) {
            byte first = fileData[0];
            byte second = fileData[1];
            headerIndicatesZip = (first == 0x50 && second == 0x4B); // "PK"
            log.info("【Bing工具】文件头检测: 第一字节=0x{}, 第二字节=0x{}, 是否ZIP: {}",
                    String.format("%02X", first & 0xFF),
                    String.format("%02X", second & 0xFF),
                    headerIndicatesZip);
        } else {
            log.warn("【Bing工具】文件数据不足，无法检测文件头");
        }

        boolean isZip = urlIndicatesZip || headerIndicatesZip;
        log.info("【Bing工具】最终判断是否为 ZIP 文件: {}", isZip);

        return isZip;
    }

    /**
     * 解压并解析 ZIP 文件（使用 Hutool）
     */
    private static List<String[]> extractAndParseZipFile(byte[] zipData) {
        List<String[]> allData = new ArrayList<>();

        log.info("【Bing工具】开始使用 Hutool 解压 ZIP 文件，数据大小: {} bytes", zipData.length);

        // 验证 ZIP 数据的有效性
        if (zipData == null || zipData.length < 22) { // ZIP 文件最小大小
            log.error("【Bing工具】ZIP 数据无效，大小不足");
            return tryParseAsPlainFile(zipData);
        }

        // 再次验证 ZIP 文件头
        if (zipData[0] != 0x50 || zipData[1] != 0x4B) {
            log.warn("【Bing工具】数据不是有效的 ZIP 格式，尝试作为普通文件解析");
            return tryParseAsPlainFile(zipData);
        }

        try {
            // 使用 Hutool 的方式处理 ZIP 文件
            log.info("【Bing工具】使用 Hutool 方式解压 ZIP 文件");

            // 创建临时文件来使用 Hutool 的 ZipUtil
            File tempZipFile = File.createTempFile("bing_report_", ".zip");
            File tempExtractDir = File.createTempFile("bing_extract_", "");
            tempExtractDir.delete(); // 删除文件，创建目录
            tempExtractDir.mkdirs();

            try {
                // 使用 Hutool 的 FileUtil 写入临时文件
                cn.hutool.core.io.FileUtil.writeBytes(zipData, tempZipFile);
                log.info("【Bing工具】写入临时 ZIP 文件: {}", tempZipFile.getAbsolutePath());

                // 使用 Hutool 的 ZipUtil 解压
                ZipUtil.unzip(tempZipFile, tempExtractDir);
                log.info("【Bing工具】解压到临时目录: {}", tempExtractDir.getAbsolutePath());

                // 遍历解压后的文件
                File[] extractedFiles = tempExtractDir.listFiles();
                if (extractedFiles != null) {
                    int fileCount = 0;
                    for (File extractedFile : extractedFiles) {
                        if (extractedFile.isDirectory()) {
                            log.info("【Bing工具】跳过目录: {}", extractedFile.getName());
                            continue;
                        }

                        fileCount++;
                        String fileName = extractedFile.getName();
                        log.info("【Bing工具】处理解压文件 #{}: {}, 大小: {} bytes",
                                fileCount, fileName, extractedFile.length());

                        // 使用 Hutool 的 FileUtil 读取文件内容
                        byte[] fileData = cn.hutool.core.io.FileUtil.readBytes(extractedFile);
                        log.info("【Bing工具】文件 {} 读取完成: {} bytes", fileName, fileData.length);

                        // 根据文件类型解析
                        try {
                            List<String[]> fileDataParsed;
                            if (isExcelFile(fileName)) {
                                log.info("【Bing工具】解析 Excel 文件: {}", fileName);
                                fileDataParsed = parseExcelContent(fileData);
                            } else if (isCsvFile(fileName)) {
                                log.info("【Bing工具】解析 CSV 文件: {}", fileName);
                                String csvContent = new String(fileData, "UTF-8");
                                fileDataParsed = parseCsvContent(csvContent);
                            } else {
                                log.warn("【Bing工具】跳过不支持的文件类型: {}", fileName);
                                continue;
                            }

                            allData.addAll(fileDataParsed);
                            log.info("【Bing工具】文件 {} 解析完成，数据行数: {}", fileName, fileDataParsed.size());

                        } catch (Exception parseEx) {
                            log.error("【Bing工具】解析文件 {} 失败，跳过该文件", fileName, parseEx);
                        }
                    }

                    log.info("【Bing工具】Hutool ZIP 解压完成，共处理 {} 个文件，总数据行数: {}", fileCount, allData.size());
                } else {
                    log.warn("【Bing工具】解压目录为空或无法读取");
                }

            } finally {
                // 清理临时文件
                try {
                    if (tempZipFile.exists()) {
                        tempZipFile.delete();
                        log.debug("【Bing工具】删除临时 ZIP 文件: {}", tempZipFile.getAbsolutePath());
                    }
                    if (tempExtractDir.exists()) {
                        cn.hutool.core.io.FileUtil.del(tempExtractDir);
                        log.debug("【Bing工具】删除临时解压目录: {}", tempExtractDir.getAbsolutePath());
                    }
                } catch (Exception cleanupEx) {
                    log.warn("【Bing工具】清理临时文件失败", cleanupEx);
                }
            }

        } catch (Exception e) {
            log.error("【Bing工具】Hutool 解压 ZIP 文件失败: {}", e.getMessage(), e);

            // 如果 ZIP 解压失败，尝试作为普通文件解析
            log.info("【Bing工具】ZIP 解压失败，尝试作为普通文件解析");
            return tryParseAsPlainFile(zipData);
        }

        return allData;
    }

    /**
     * 尝试将数据作为普通文件解析
     */
    private static List<String[]> tryParseAsPlainFile(byte[] fileData) {
        if (fileData == null || fileData.length == 0) {
            log.warn("【Bing工具】文件数据为空，无法解析");
            return new ArrayList<>();
        }

        try {
            log.info("【Bing工具】尝试作为普通文件解析，数据大小: {} bytes", fileData.length);

            // 尝试作为 CSV 解析
            String content = new String(fileData, "UTF-8");

            // 检查是否包含 CSV 特征（逗号分隔、换行符等）
            if (content.contains(",") || content.contains("\n") || content.contains("\r")) {
                log.info("【Bing工具】检测到 CSV 特征，作为 CSV 解析");
                return parseCsvContent(content);
            } else {
                log.warn("【Bing工具】无法识别文件格式，返回空数据");
                return new ArrayList<>();
            }

        } catch (Exception e) {
            log.error("【Bing工具】作为普通文件解析也失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 判断是否为 CSV 文件
     */
    private static boolean isCsvFile(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return false;
        }
        String lowerName = fileName.toLowerCase();
        return lowerName.endsWith(".csv") || lowerName.endsWith(".txt");
    }

    /**
     * 判断是否为 Excel 文件
     */
    private static boolean isExcelFile(String downloadUrl) {
        if (StringUtils.isBlank(downloadUrl)) {
            return false;
        }
        String lowerUrl = downloadUrl.toLowerCase();
        return lowerUrl.contains(".xlsx") || lowerUrl.contains(".xls") || lowerUrl.contains("excel");
    }

    /**
     * 使用 EasyExcel 解析 Excel 内容
     */
    private static List<String[]> parseExcelContent(byte[] fileData) {
        List<String[]> excelData = new ArrayList<>();

        try {
            log.info("【Bing工具】开始使用 EasyExcel 解析 Excel 内容");

            // 使用字节数组创建输入流
            ByteArrayInputStream inputStream = new ByteArrayInputStream(fileData);

            // 使用 EasyExcel 读取数据
            EasyExcel.read(inputStream, new AnalysisEventListener<Map<Integer, String>>() {
                @Override
                public void invoke(Map<Integer, String> data, AnalysisContext context) {
                    // 将 Map 转换为 String 数组
                    List<String> rowData = new ArrayList<>();
                    int maxIndex = data.keySet().stream().mapToInt(Integer::intValue).max().orElse(-1);

                    for (int i = 0; i <= maxIndex; i++) {
                        rowData.add(data.getOrDefault(i, ""));
                    }

                    excelData.add(rowData.toArray(new String[0]));
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    log.info("【Bing工具】EasyExcel 解析完成，共{}行数据", excelData.size());
                }
            }).sheet().doRead();

        } catch (Exception e) {
            log.error("【Bing工具】EasyExcel 解析 Excel 内容失败", e);
            // 如果 Excel 解析失败，尝试作为 CSV 解析
            log.info("【Bing工具】尝试将内容作为 CSV 解析");
            return parseCsvContent(excelData.toString());
        }

        return excelData;
    }

    /**
     * 解析 CSV 内容
     */
    private static List<String[]> parseCsvContent(String csvContent) {
        List<String[]> csvData = new ArrayList<>();

        try {
            log.info("【Bing工具】开始解析 CSV 内容");

            String[] lines = csvContent.split("\\r?\\n");
            for (String line : lines) {
                if (StringUtils.isNotBlank(line)) {
                    // 简单的 CSV 解析，处理逗号分隔的字段
                    // 注意：这里可能需要更复杂的 CSV 解析逻辑来处理引号内的逗号
                    String[] fields = parseCsvLine(line);
                    csvData.add(fields);
                }
            }

            log.info("【Bing工具】CSV 解析完成，共{}行数据", csvData.size());

        } catch (Exception e) {
            log.error("【Bing工具】解析 CSV 内容失败", e);
        }

        return csvData;
    }

    /**
     * 解析单行 CSV 数据，处理引号内的逗号
     */
    private static String[] parseCsvLine(String line) {
        List<String> fields = new ArrayList<>();
        StringBuilder currentField = new StringBuilder();
        boolean inQuotes = false;

        for (int i = 0; i < line.length(); i++) {
            char c = line.charAt(i);

            if (c == '"') {
                inQuotes = !inQuotes;
            } else if (c == ',' && !inQuotes) {
                fields.add(currentField.toString().trim());
                currentField.setLength(0);
            } else {
                currentField.append(c);
            }
        }

        // 添加最后一个字段
        fields.add(currentField.toString().trim());

        return fields.toArray(new String[0]);
    }

    /**
     * 报告状态类
     */
    public static class ReportStatus {
        private String status;
        private String downloadUrl;

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getDownloadUrl() {
            return downloadUrl;
        }

        public void setDownloadUrl(String downloadUrl) {
            this.downloadUrl = downloadUrl;
        }

        public boolean isSuccess() {
            return "Success".equalsIgnoreCase(status);
        }

        public boolean isPending() {
            return "Pending".equalsIgnoreCase(status) || "InProgress".equalsIgnoreCase(status);
        }

        public boolean isError() {
            return "Error".equalsIgnoreCase(status) || "Failed".equalsIgnoreCase(status);
        }
    }
}
