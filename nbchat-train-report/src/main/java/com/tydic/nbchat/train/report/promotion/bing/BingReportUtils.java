package com.tydic.nbchat.train.report.promotion.bing;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/6/26
 * @description Bing广告报告工具类
 */
@Slf4j
public class BingReportUtils {

    private static final String BING_REPORTING_API_URL = "https://reporting.api.bingads.microsoft.com/Reporting/v13/GenerateReport/Submit";
    private static final String BING_POLL_API_URL = "https://reporting.api.bingads.microsoft.com/Reporting/v13/GenerateReport/Poll";

    /**
     * 使用REST API提交报告生成请求
     */
    public static String submitReportRequestWithRestApi(String customerId, String customerAccountId,
                                                       String developerToken, String accessToken,
                                                       Date startDate, Date endDate, String deviceFilter) {
        try {
            log.info("【Bing工具】使用REST API提交报告请求，customerId: {}, customerAccountId: {}", customerId, customerAccountId);

            // 构建请求体
            JSONObject reportRequest = buildReportRequestJson(customerId, customerAccountId, startDate, endDate, deviceFilter);

            // 构建完整请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("ReportRequest", reportRequest);

            String requestJson = requestBody.toJSONString();
            log.debug("【Bing工具】请求体: {}", requestJson);

            // 发送HTTP请求
            HttpURLConnection connection = createHttpConnection(BING_REPORTING_API_URL, "POST");
            setRequestHeaders(connection, customerId, customerAccountId, developerToken, accessToken);

            // 发送请求体
            connection.getOutputStream().write(requestJson.getBytes("UTF-8"));

            int responseCode = connection.getResponseCode();
            log.info("【Bing工具】提交报告请求响应码: {}", responseCode);

            if (responseCode == 200) {
                String response = readResponse(connection);
                log.info("【Bing工具】提交报告请求成功，响应: {}", response);

                // 解析响应获取报告请求ID
                JSONObject responseJson = JSON.parseObject(response);
                return responseJson.getString("ReportRequestId");
            } else {
                String errorResponse = readErrorResponse(connection);
                log.error("【Bing工具】提交报告请求失败，响应码: {}, 错误: {}", responseCode, errorResponse);
                return null;
            }

        } catch (Exception e) {
            log.error("【Bing工具】提交报告请求异常", e);
            return null;
        }
    }

    /**
     * 构建报告请求JSON
     */
    private static JSONObject buildReportRequestJson(String customerId, String customerAccountId,
                                                   Date startDate, Date endDate, String deviceFilter) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        JSONObject reportRequest = new JSONObject();
        reportRequest.put("Type", "SearchQueryPerformanceReportRequest");
        reportRequest.put("ReportName", "SearchQueryPerformance_" + sdf.format(new Date()).replace("-", "_"));
        reportRequest.put("Format", "Csv");
        reportRequest.put("Aggregation", "Daily");
        reportRequest.put("ReturnOnlyCompleteData", false);
        reportRequest.put("ExcludeReportHeader", true);
        reportRequest.put("ExcludeReportFooter", true);
        reportRequest.put("ExcludeColumnHeaders", false);

        // 时间范围
        JSONObject time = new JSONObject();
        JSONObject startDateObj = new JSONObject();
        String[] startParts = sdf.format(startDate).split("-");
        startDateObj.put("Year", Integer.parseInt(startParts[0]));
        startDateObj.put("Month", Integer.parseInt(startParts[1]));
        startDateObj.put("Day", Integer.parseInt(startParts[2]));

        JSONObject endDateObj = new JSONObject();
        String[] endParts = sdf.format(endDate).split("-");
        endDateObj.put("Year", Integer.parseInt(endParts[0]));
        endDateObj.put("Month", Integer.parseInt(endParts[1]));
        endDateObj.put("Day", Integer.parseInt(endParts[2]));

        time.put("CustomDateRangeStart", startDateObj);
        time.put("CustomDateRangeEnd", endDateObj);
        reportRequest.put("Time", time);

        // 账户范围
        if (StringUtils.isNotBlank(customerAccountId)) {
            JSONObject scope = new JSONObject();
            List<Long> accountIds = new ArrayList<>();
            accountIds.add(Long.parseLong(customerAccountId));
            scope.put("AccountIds", accountIds);
            reportRequest.put("Scope", scope);
        }

        // 列定义
        List<String> columns = Arrays.asList(
            "TimePeriod", "SearchQuery", "CampaignName", "AdGroupName",
            "Keyword", "Impressions", "Clicks", "Spend", "Ctr",
            "AverageCpc", "DeviceType", "DeliveredMatchType"
        );
        reportRequest.put("Columns", columns);

        // 过滤器
        if (StringUtils.isNotBlank(deviceFilter)) {
            JSONObject filter = new JSONObject();
            filter.put("DeviceType", deviceFilter);
            reportRequest.put("Filter", filter);
        }

        return reportRequest;
    }

    /**
     * 创建HTTP连接
     */
    private static HttpURLConnection createHttpConnection(String url, String method) throws Exception {
        URL urlObj = new URL(url);
        HttpURLConnection connection = (HttpURLConnection) urlObj.openConnection();
        connection.setRequestMethod(method);
        connection.setDoOutput(true);
        connection.setDoInput(true);
        connection.setConnectTimeout(30000);
        connection.setReadTimeout(60000);
        return connection;
    }

    /**
     * 设置请求头
     */
    private static void setRequestHeaders(HttpURLConnection connection, String customerId,
                                        String customerAccountId, String developerToken, String accessToken) {
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("Accept-Encoding", "gzip, deflate, br");
        connection.setRequestProperty("Authorization", "Bearer " + accessToken);
        connection.setRequestProperty("Connection", "keep-alive");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("CustomerAccountId", customerAccountId);
        connection.setRequestProperty("CustomerId", customerId);
        connection.setRequestProperty("DeveloperToken", developerToken);
        connection.setRequestProperty("User-Agent", "NBChat-BingAds-Client/1.0");
    }

    /**
     * 读取响应
     */
    private static String readResponse(HttpURLConnection connection) throws Exception {
        BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
        StringBuilder response = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            response.append(line);
        }
        reader.close();
        return response.toString();
    }

    /**
     * 读取错误响应
     */
    private static String readErrorResponse(HttpURLConnection connection) throws Exception {
        BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
        StringBuilder response = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            response.append(line);
        }
        reader.close();
        return response.toString();
    }



    /**
     * 使用REST API轮询报告状态
     */
    public static ReportStatus pollReportStatusWithRestApi(String customerId, String customerAccountId,
                                                          String developerToken, String accessToken,
                                                          String reportRequestId) {
        try {
            log.info("【Bing工具】使用REST API轮询报告状态，reportRequestId: {}", reportRequestId);

            // 构建轮询URL
            String pollUrl = BING_POLL_API_URL + "?ReportRequestId=" + reportRequestId;

            // 创建HTTP连接
            HttpURLConnection connection = createHttpConnection(pollUrl, "GET");
            setRequestHeaders(connection, customerId, customerAccountId, developerToken, accessToken);

            int responseCode = connection.getResponseCode();
            log.info("【Bing工具】轮询报告状态响应码: {}", responseCode);

            if (responseCode == 200) {
                String response = readResponse(connection);
                log.debug("【Bing工具】轮询报告状态响应: {}", response);

                // 解析响应
                JSONObject responseJson = JSON.parseObject(response);
                ReportStatus status = new ReportStatus();
                status.setStatus(responseJson.getString("ReportRequestStatus"));
                status.setDownloadUrl(responseJson.getString("ReportDownloadUrl"));

                log.info("【Bing工具】报告状态: {}, 下载URL: {}", status.getStatus(), status.getDownloadUrl());
                return status;
            } else {
                String errorResponse = readErrorResponse(connection);
                log.error("【Bing工具】轮询报告状态失败，响应码: {}, 错误: {}", responseCode, errorResponse);
                return null;
            }

        } catch (Exception e) {
            log.error("【Bing工具】轮询报告状态异常", e);
            return null;
        }
    }





    /**
     * 下载CSV报告数据
     */
    public static List<String[]> downloadCsvReport(String downloadUrl) {
        List<String[]> csvData = new ArrayList<>();

        try {
            log.info("【Bing工具】开始下载CSV报告，URL: {}", downloadUrl);

            URL url = new URL(downloadUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(30000); // 30秒连接超时
            connection.setReadTimeout(60000); // 60秒读取超时
            connection.setRequestProperty("User-Agent", "NBChat-BingAds-Client/1.0");

            int responseCode = connection.getResponseCode();
            log.info("【Bing工具】CSV下载响应码: {}", responseCode);

            if (responseCode != 200) {
                log.error("【Bing工具】CSV下载失败，响应码: {}", responseCode);
                return csvData;
            }

            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String line;
            boolean isFirstLine = true;
            int lineCount = 0;

            while ((line = reader.readLine()) != null) {
                lineCount++;

                // 跳过标题行
                if (isFirstLine) {
                    isFirstLine = false;
                    log.info("【Bing工具】CSV标题行: {}", line);
                    continue;
                }

                // 解析CSV行
                String[] fields = parseCsvLine(line);
                if (fields != null && fields.length > 0) {
                    csvData.add(fields);
                }
            }

            reader.close();
            connection.disconnect();

            log.info("【Bing工具】CSV下载完成，总行数: {}，数据行数: {}", lineCount, csvData.size());

        } catch (Exception e) {
            log.error("【Bing工具】下载CSV报告失败，URL: " + downloadUrl, e);
        }

        return csvData;
    }

    /**
     * 解析CSV行，处理引号和逗号
     */
    private static String[] parseCsvLine(String line) {
        if (StringUtils.isBlank(line)) {
            return null;
        }
        
        List<String> fields = new ArrayList<>();
        StringBuilder currentField = new StringBuilder();
        boolean inQuotes = false;
        
        for (int i = 0; i < line.length(); i++) {
            char c = line.charAt(i);
            
            if (c == '"') {
                inQuotes = !inQuotes;
            } else if (c == ',' && !inQuotes) {
                fields.add(currentField.toString().trim());
                currentField = new StringBuilder();
            } else {
                currentField.append(c);
            }
        }
        
        // 添加最后一个字段
        fields.add(currentField.toString().trim());
        
        return fields.toArray(new String[0]);
    }

    /**
     * 报告状态类
     */
    public static class ReportStatus {
        private String status;
        private String downloadUrl;

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getDownloadUrl() {
            return downloadUrl;
        }

        public void setDownloadUrl(String downloadUrl) {
            this.downloadUrl = downloadUrl;
        }

        public boolean isSuccess() {
            return "Success".equals(status);
        }

        public boolean isPending() {
            return "Pending".equals(status);
        }

        public boolean isError() {
            return "Error".equals(status);
        }
    }
}
