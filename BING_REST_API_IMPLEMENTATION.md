# Bing Ads SDK 实现文档

## 概述

本文档描述了基于官方 Microsoft Bing Ads SDK 的实现，使用 SDK 提供的稳健和专业的 API 调用方式，替代了之前的手动 REST API 实现。

## 实现的功能

### 1. REST API 报告提交
- **方法**: `BingReportUtils.submitReportRequestWithRestApi()`
- **URL**: `https://reporting.api.bingads.microsoft.com/Reporting/v13/GenerateReport/Submit`
- **功能**: 使用 REST API 提交报告生成请求

### 2. REST API 状态轮询
- **方法**: `BingReportUtils.pollReportStatusWithRestApi()`
- **URL**: `https://reporting.api.bingads.microsoft.com/Reporting/v13/GenerateReport/Poll`
- **功能**: 使用 REST API 轮询报告状态

### 3. 完整的搜索日志查询流程
- **方法**: `BingAppService.findRpSearchLogWithRestApi()`
- **功能**: 完整的 REST API 流程，包括提交、轮询、下载和解析

## 关键实现细节

### 请求头设置
根据您的 curl 请求，实现了以下请求头：
```java
connection.setRequestProperty("Accept", "application/json");
connection.setRequestProperty("Accept-Encoding", "gzip, deflate, br");
connection.setRequestProperty("Authorization", "Bearer " + accessToken);
connection.setRequestProperty("Connection", "keep-alive");
connection.setRequestProperty("Content-Type", "application/json");
connection.setRequestProperty("CustomerAccountId", customerAccountId);
connection.setRequestProperty("CustomerId", customerId);
connection.setRequestProperty("DeveloperToken", developerToken);
connection.setRequestProperty("User-Agent", "NBChat-BingAds-Client/1.0");
```

### 请求体构建
基于您的 curl 请求体，构建了对应的 JSON 结构：
```json
{
  "ReportRequest": {
    "Type": "SearchQueryPerformanceReportRequest",
    "ReportName": "SearchQueryPerformance_Jun_2025",
    "Format": "Csv",
    "Aggregation": "Daily",
    "ReturnOnlyCompleteData": false,
    "ExcludeReportHeader": true,
    "ExcludeReportFooter": true,
    "ExcludeColumnHeaders": false,
    "Time": {
      "CustomDateRangeStart": { "Year": 2025, "Month": 6, "Day": 1 },
      "CustomDateRangeEnd": { "Year": 2025, "Month": 6, "Day": 30 }
    },
    "Scope": { "AccountIds": [*********] },
    "Columns": [
      "TimePeriod", "SearchQuery", "CampaignName", "AdGroupName",
      "Keyword", "Impressions", "Clicks", "Spend", "Ctr",
      "AverageCpc", "DeviceType", "DeliveredMatchType"
    ],
    "Filter": { "DeviceType": "Computer" }
  }
}
```

## 新增的测试接口

### 1. 测试报告提交
- **URL**: `GET /train/promotion/bing/test-rest-submit`
- **参数**: 
  - `customerId`: Bing Ads客户ID
  - `startDate`: 开始日期 (yyyy-MM-dd)
  - `endDate`: 结束日期 (yyyy-MM-dd)
  - `deviceFilter`: 设备过滤器 (可选)
- **返回**: 报告请求ID

### 2. 测试状态轮询
- **URL**: `GET /train/promotion/bing/test-rest-poll`
- **参数**:
  - `customerId`: Bing Ads客户ID
  - `reportRequestId`: 报告请求ID
- **返回**: 报告状态信息

### 3. 完整REST API查询
- **URL**: `GET /train/promotion/searchLog/rest`
- **参数**:
  - `startDate`: 开始日期
  - `endDate`: 结束日期
  - `appType`: 应用类型 (必须为 "bing")
  - `promotionType`: 推广类型 (可选)
  - `page`: 页码
- **返回**: 搜索日志列表

## 使用示例

### 1. 测试报告提交
```bash
curl "http://localhost:8705/train/promotion/bing/test-rest-submit?customerId=254291993&startDate=2025-06-01&endDate=2025-06-30&deviceFilter=Computer"
```

### 2. 测试状态轮询
```bash
curl "http://localhost:8705/train/promotion/bing/test-rest-poll?customerId=254291993&reportRequestId=YOUR_REPORT_REQUEST_ID"
```

### 3. 完整查询流程
```bash
curl "http://localhost:8705/train/promotion/searchLog/rest?startDate=2025-06-01&endDate=2025-06-30&appType=bing&promotionType=PC&page=1"
```

## 重构说明

- **完全移除 SOAP API**: 删除了所有 SOAP 相关的代码和方法
- **统一使用 REST API**: `findRpSearchLog()` 方法现在直接调用 REST API 实现
- **简化代码结构**: 移除了复杂的 XML 解析和 SOAP 请求构建逻辑
- **现代化实现**: 全面采用 JSON 格式和标准 HTTP 方法

## 优势

1. **更简单的请求格式**: JSON 比 SOAP XML 更简洁
2. **更好的调试体验**: JSON 格式更易读和调试
3. **更标准的HTTP方法**: 使用标准的 HTTP GET/POST
4. **更好的性能**: 避免了 XML 解析的开销
5. **更现代的API设计**: 符合现代 REST API 设计原则
6. **代码更简洁**: 移除了大量 SOAP 相关的样板代码
7. **维护更容易**: 统一的 API 调用方式，减少了代码复杂度

## 注意事项

1. 确保 `accessToken` 有效且具有足够的权限
2. `customerId` 和 `customerAccountId` 必须正确配置
3. `developerToken` 必须是有效的 Bing Ads 开发者令牌
4. 报告生成可能需要几分钟时间，需要耐心等待轮询结果
