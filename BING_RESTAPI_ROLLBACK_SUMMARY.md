# Bing Ads RestApiHelper 回退总结

## 回退概述

根据您的要求，我们已经从 Bing Ads SDK 回退到使用 `restApiHelper` 的方式，并且在解析 JSON 之前增加了一道流程来正确提取响应中的 JSON 字符串。

## 主要变更

### 1. 移除 SDK 依赖

从 `nbchat-train-report/pom.xml` 中移除了 Microsoft Bing Ads SDK 依赖：

```xml
<!-- 已移除 -->
<dependency>
    <groupId>com.microsoft.bingads</groupId>
    <artifactId>microsoft.bingads</artifactId>
    <version>13.0.24.3</version>
</dependency>
```

### 2. 重构 BingReportUtils.java

#### 重新创建了干净的实现
- 移除了所有 SDK 相关的导入和代码
- 恢复使用 `RestApiHelper` 进行 HTTP 调用
- 添加了关键的 JSON 提取逻辑

#### 核心改进：JSON 提取流程

**新增的 `extractJsonFromResponse()` 方法**：
```java
/**
 * 从响应中提取 JSON 字符串
 * 处理可能的响应格式：纯JSON、包含其他内容的响应等
 */
private static String extractJsonFromResponse(String responseBody) {
    if (StringUtils.isBlank(responseBody)) {
        return null;
    }

    try {
        // 去除前后空白字符
        String trimmed = responseBody.trim();
        
        // 如果响应直接是 JSON 格式，直接返回
        if (trimmed.startsWith("{") && trimmed.endsWith("}")) {
            log.debug("【Bing工具】响应是纯 JSON 格式");
            return trimmed;
        }
        
        // 如果响应包含其他内容，尝试提取 JSON 部分
        int jsonStart = trimmed.indexOf("{");
        int jsonEnd = trimmed.lastIndexOf("}");
        
        if (jsonStart >= 0 && jsonEnd > jsonStart) {
            String extractedJson = trimmed.substring(jsonStart, jsonEnd + 1);
            log.debug("【Bing工具】从响应中提取的 JSON: {}", extractedJson);
            
            // 验证提取的字符串是否为有效 JSON
            JSON.parseObject(extractedJson);
            return extractedJson;
        }
        
        log.warn("【Bing工具】无法从响应中找到有效的 JSON 格式: {}", responseBody);
        return null;
        
    } catch (Exception e) {
        log.error("【Bing工具】提取 JSON 字符串时发生异常，响应内容: {}", responseBody, e);
        return null;
    }
}
```

### 3. 改进的请求处理流程

#### 提交报告请求流程：
```java
public static String submitReportRequestWithRestApi(RestApiHelper restApiHelper, ...) {
    // 1. 构建请求体
    JSONObject requestBody = new JSONObject();
    requestBody.put("ReportRequest", buildReportRequestJson(...));
    
    // 2. 构建请求头
    HttpHeaders headers = buildRequestHeaders(...);
    
    // 3. 发送请求
    String responseBody = restApiHelper.post(BING_REPORTING_API_URL, requestJson, headers);
    
    // 4. 关键步骤：提取 JSON 字符串
    String jsonString = extractJsonFromResponse(responseBody);
    if (StringUtils.isBlank(jsonString)) {
        log.error("【Bing工具】无法从响应中提取有效的 JSON 字符串");
        return null;
    }
    
    // 5. 解析 JSON 获取结果
    JSONObject responseJson = JSON.parseObject(jsonString);
    return responseJson.getString("ReportRequestId");
}
```

#### 轮询状态流程：
```java
public static ReportStatus pollReportStatusWithRestApi(RestApiHelper restApiHelper, ...) {
    // 1. 构建轮询URL和请求头
    String pollUrl = BING_POLL_API_URL + "?ReportRequestId=" + reportRequestId;
    HttpHeaders headers = buildRequestHeaders(...);
    
    // 2. 发送GET请求
    String responseBody = restApiHelper.get(pollUrl, headers);
    
    // 3. 关键步骤：提取 JSON 字符串
    String jsonString = extractJsonFromResponse(responseBody);
    if (StringUtils.isBlank(jsonString)) {
        log.error("【Bing工具】无法从轮询响应中提取有效的 JSON 字符串");
        return null;
    }
    
    // 4. 解析状态信息
    JSONObject responseJson = JSON.parseObject(jsonString);
    ReportStatus status = new ReportStatus();
    status.setStatus(responseJson.getString("ReportRequestStatus"));
    status.setDownloadUrl(responseJson.getString("ReportDownloadUrl"));
    
    return status;
}
```

### 4. 方法名称更新

所有方法都从 `Sdk` 回退到 `RestApi`：

- `submitReportRequestWithSdk` → `submitReportRequestWithRestApi`
- `pollReportStatusWithSdk` → `pollReportStatusWithRestApi`
- `findRpSearchLogWithSdk` → `findRpSearchLogWithRestApi`
- `testSubmitReportRequestWithSdk` → `testSubmitReportRequestWithRestApi`
- `testPollReportStatusWithSdk` → `testPollReportStatusWithRestApi`

### 5. 控制器端点更新

测试接口也相应回退：

- `/bing/test-sdk-submit` → `/bing/test-rest-submit`
- `/bing/test-sdk-poll` → `/bing/test-rest-poll`
- `/searchLog/sdk` → `/searchLog/rest`

## 关键改进：JSON 提取处理

### 问题背景
在直接解析 JSON 之前，需要确保从 HTTP 响应中正确提取出 JSON 字符串，因为响应可能包含：
- 纯 JSON 格式
- 包含其他内容的混合响应
- 带有额外空白字符的响应

### 解决方案
新增的 `extractJsonFromResponse()` 方法能够：

1. **处理纯 JSON 响应**：
   ```json
   {"ReportRequestId": "12345", "Status": "Success"}
   ```

2. **处理混合内容响应**：
   ```
   HTTP/1.1 200 OK
   Content-Type: application/json
   
   {"ReportRequestId": "12345", "Status": "Success"}
   
   Additional content...
   ```

3. **验证 JSON 有效性**：
   - 提取后立即验证是否为有效 JSON
   - 避免后续解析错误

4. **详细日志记录**：
   - 记录提取过程
   - 便于调试和问题排查

## 测试接口

### 1. 测试 RestApiHelper 报告提交
```bash
curl "http://localhost:8705/train/promotion/bing/test-rest-submit?customerId=254291993&startDate=2025-06-01&endDate=2025-06-30&deviceFilter=Computer"
```

### 2. 测试 RestApiHelper 状态轮询
```bash
curl "http://localhost:8705/train/promotion/bing/test-rest-poll?customerId=254291993&reportRequestId=YOUR_REPORT_REQUEST_ID"
```

### 3. 完整 RestApiHelper 查询流程
```bash
curl "http://localhost:8705/train/promotion/searchLog/rest?startDate=2025-06-01&endDate=2025-06-30&appType=bing&page=1"
```

## 优势

### 1. 更稳健的响应处理
- **智能 JSON 提取**: 能够处理各种响应格式
- **错误容错**: 提取失败时有详细的错误日志
- **验证机制**: 确保提取的字符串是有效 JSON

### 2. 简化的依赖管理
- **无外部 SDK**: 减少依赖复杂性
- **轻量级**: 只使用必要的 HTTP 客户端
- **可控性**: 完全控制请求和响应处理

### 3. 更好的调试体验
- **详细日志**: 记录每个处理步骤
- **响应内容**: 完整记录原始响应
- **错误追踪**: 精确定位问题所在

### 4. 灵活性
- **自定义处理**: 可以根据需要调整 JSON 提取逻辑
- **扩展性**: 容易添加新的响应格式支持
- **维护性**: 代码结构清晰，易于维护

## 总结

通过回退到 `restApiHelper` 并增加 JSON 提取流程，我们实现了：

✅ **更稳健的响应处理**: 智能提取和验证 JSON 字符串  
✅ **简化的架构**: 移除 SDK 依赖，减少复杂性  
✅ **更好的错误处理**: 详细的日志和错误追踪  
✅ **完整的功能**: 保持所有原有功能不变  
✅ **易于调试**: 清晰的处理流程和日志记录  

现在 Bing Ads 集成更加可控、稳健，并且能够正确处理各种响应格式！
