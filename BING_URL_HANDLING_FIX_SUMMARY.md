# Bing Ads URL 处理修复总结

## 问题分析

### 错误现象
```
2025-07-02 11:22:44.940 |-ERROR [http-nio-8705-exec-6] com.tydic.nicc.dc.boot.starter.http.RestApiHelper [242] -| 接口调用失败:statusCode = 403 FORBIDDEN, errMsg = 403 Server failed to authenticate the request. Make sure the value of Authorization header is formed correctly including the signature.: "﻿<?xml version="1.0" encoding="utf-8"?><Error><Code>AuthenticationFailed</Code><Message>Server failed to authenticate the request. Make sure the value of Authorization header is formed correctly including the signature.<EOL>RequestId:06ceac74-101e-0069-0b00-eb9dc9000000<EOL>Time:2025-07-02T03:22:44.8356882Z</Message><AuthenticationErrorDetail>Signature fields not well formed.</AuthenticationErrorDetail></Error>"
```

### 根本原因
从日志可以看出，URL 在传递给 `restApiHelper.get()` 时被错误处理了：

**原始 URL**:
```
https://bingadsappsstorageprod.blob.core.windows.net:443/oo9-reportdataapi-25-07-02/626c15c9-bd17-4bb8-a5f3-8c77ecc74069/SearchQueryPerformance_2025_07_02.zip?skoid=e697f752-fbaf-4c0b-999c-e188178a9ed3&sktid=975f013f-7f24-47e8-a7d3-abc4752bf346&skt=2025-07-01T22%3A37%3A48Z&ske=2025-07-08T22%3A37%3A48Z&sks=b&skv=2019-12-12&sv=2025-05-05&st=2025-07-02T03%3A17%3A40Z&se=2025-07-02T03%3A32%3A40Z&sr=b&sp=r&sig=%2FyCBOxNwZes5JBBHQ9XXZp4RljgQZ7Zatki7WLmC6yI%3D
```

**错误处理后的 URL**:
```
https://bingadsappsstorageprod.blob.core.windows.net:443/oo9-reportdataapi-25-07-02/626c15c9-bd17-4bb8-a5f3-8c77ecc74069/SearchQueryPerformance_2025_07_02.zip?skoid=e697f752-fbaf-4c0b-999c-e188178a9ed3&sktid=975f013f-7f24-47e8-a7d3-abc4752bf346&skt=2025-07-01T22%253A37%253A48Z&ske=2025-07-08T22%253A37%253A48Z&sks=b&skv=2019-12-12&sv=2025-05-05&st=2025-07-02T03%253A17%253A40Z&se=2025-07-02T03%253A32%253A40Z&sr=b&sp=r&sig=%252FyCBOxNwZes5JBBHQ9XXZp4RljgQZ7Zatki7WLmC6yI%253Dnull
```

**问题分析**:
1. URL 参数被重复编码（`%3A` 变成了 `%253A`）
2. 签名参数被损坏（末尾多了 `null`）
3. Azure Blob Storage 无法验证损坏的签名

## 解决方案

### 1. RestApiHelper 正确用法

#### 错误的调用方式：
```java
// 错误：将完整 URL 作为第一个参数
String responseStr = restApiHelper.get(downloadUrl, null, null, null);
```

#### 正确的调用方式：
```java
// 正确：分离 host 和 pathAndQuery
java.net.URI uri = java.net.URI.create(downloadUrl);
String host = uri.getScheme() + "://" + uri.getHost();
if (uri.getPort() != -1) {
    host += ":" + uri.getPort();
}
String pathAndQuery = uri.getPath();
if (StringUtils.isNotBlank(uri.getQuery())) {
    pathAndQuery += "?" + uri.getQuery();
}

String responseStr = restApiHelper.get(host, pathAndQuery, null, headers);
```

### 2. RestApiHelper.get() 方法签名

根据源码分析，正确的方法签名是：
```java
public String get(String host, String pathAndQuery, Map<String, Object> params, HttpHeaders headers, MediaType... types)
```

**参数说明**:
- `host`: 主机地址，如 `https://example.com:443`
- `pathAndQuery`: 路径和查询参数，如 `/path/to/resource?param=value`
- `params`: 额外的查询参数（会被添加到 URL 中）
- `headers`: HTTP 请求头
- `types`: 接受的媒体类型

### 3. URL 解析实现

#### 完整的 URL 解析逻辑：
```java
private static byte[] downloadBinaryFile(RestApiHelper restApiHelper, String downloadUrl) {
    try {
        log.info("【Bing工具】开始下载二进制文件: {}", downloadUrl);

        // 解析 URL 以正确使用 restApiHelper
        java.net.URI uri = java.net.URI.create(downloadUrl);
        String host = uri.getScheme() + "://" + uri.getHost();
        if (uri.getPort() != -1) {
            host += ":" + uri.getPort();
        }
        String pathAndQuery = uri.getPath();
        if (StringUtils.isNotBlank(uri.getQuery())) {
            pathAndQuery += "?" + uri.getQuery();
        }

        log.info("【Bing工具】解析URL - host: {}, pathAndQuery: {}", host, pathAndQuery);

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.set("Accept", "*/*");
        headers.set("User-Agent", "NBChat-BingAds-Client/1.0");

        // 使用正确的 restApiHelper.get 方法
        String responseStr = restApiHelper.get(host, pathAndQuery, null, headers);

        // 处理响应...
    } catch (Exception e) {
        log.error("【Bing工具】下载二进制文件异常", e);
        return null;
    }
}
```

## URL 处理对比

### 1. Azure Blob Storage URL 结构
```
https://bingadsappsstorageprod.blob.core.windows.net:443/oo9-reportdataapi-25-07-02/626c15c9-bd17-4bb8-a5f3-8c77ecc74069/SearchQueryPerformance_2025_07_02.zip?skoid=e697f752-fbaf-4c0b-999c-e188178a9ed3&sktid=975f013f-7f24-47e8-a7d3-abc4752bf346&skt=2025-07-01T22%3A37%3A48Z&ske=2025-07-08T22%3A37%3A48Z&sks=b&skv=2019-12-12&sv=2025-05-05&st=2025-07-02T03%3A17%3A40Z&se=2025-07-02T03%3A32%3A40Z&sr=b&sp=r&sig=%2FyCBOxNwZes5JBBHQ9XXZp4RljgQZ7Zatki7WLmC6yI%3D
```

### 2. 正确的解析结果
```
host: https://bingadsappsstorageprod.blob.core.windows.net:443
pathAndQuery: /oo9-reportdataapi-25-07-02/626c15c9-bd17-4bb8-a5f3-8c77ecc74069/SearchQueryPerformance_2025_07_02.zip?skoid=e697f752-fbaf-4c0b-999c-e188178a9ed3&sktid=975f013f-7f24-47e8-a7d3-abc4752bf346&skt=2025-07-01T22%3A37%3A48Z&ske=2025-07-08T22%3A37%3A48Z&sks=b&skv=2019-12-12&sv=2025-05-05&st=2025-07-02T03%3A17%3A40Z&se=2025-07-02T03%3A32%3A40Z&sr=b&sp=r&sig=%2FyCBOxNwZes5JBBHQ9XXZp4RljgQZ7Zatki7WLmC6yI%3D
```

### 3. Azure SAS 签名参数
Azure Blob Storage 使用 SAS (Shared Access Signature) 进行身份验证，关键参数包括：
- `skoid`: Storage Key Object ID
- `sktid`: Storage Key Tenant ID  
- `skt`: Start Time
- `ske`: Expiry Time
- `sig`: Signature（最关键，不能被修改）

## 调试信息增强

### 1. URL 解析日志
```java
log.info("【Bing工具】解析URL - host: {}, pathAndQuery: {}", host, pathAndQuery);
```

### 2. 请求状态日志
```java
log.info("【Bing工具】开始下载二进制文件: {}", downloadUrl);
log.info("【Bing工具】下载完成，数据大小: {} bytes", data.length);
```

### 3. 文件头调试
```java
if (data.length >= 10) {
    StringBuilder hexStr = new StringBuilder();
    for (int i = 0; i < Math.min(10, data.length); i++) {
        hexStr.append(String.format("%02X ", data[i] & 0xFF));
    }
    log.info("【Bing工具】文件头字节: {}", hexStr.toString());
}
```

## 其他 RestApiHelper 使用示例

### 1. 简单 GET 请求
```java
// 基本用法
String response = restApiHelper.get("https://api.example.com", "/users", null);

// 带参数
Map<String, Object> params = new HashMap<>();
params.put("page", 1);
params.put("size", 10);
String response = restApiHelper.get("https://api.example.com", "/users", params);
```

### 2. 带请求头的 GET 请求
```java
HttpHeaders headers = new HttpHeaders();
headers.set("Authorization", "Bearer token");
headers.set("Accept", "application/json");

String response = restApiHelper.get("https://api.example.com", "/users", null, headers);
```

### 3. POST 请求
```java
HttpHeaders headers = new HttpHeaders();
headers.set("Content-Type", "application/json");

String requestBody = "{\"name\":\"test\"}";
String response = restApiHelper.post("https://api.example.com/users", requestBody, headers);
```

## 测试验证

### 1. URL 解析测试
```java
String testUrl = "https://example.com:443/path/to/resource?param1=value1&param2=value2";
java.net.URI uri = java.net.URI.create(testUrl);
String host = uri.getScheme() + "://" + uri.getHost() + ":" + uri.getPort();
String pathAndQuery = uri.getPath() + "?" + uri.getQuery();

// 验证解析结果
assertEquals("https://example.com:443", host);
assertEquals("/path/to/resource?param1=value1&param2=value2", pathAndQuery);
```

### 2. Azure Blob 下载测试
```java
// 测试真实的 Azure Blob Storage URL
String azureUrl = "https://storage.blob.core.windows.net/container/file.zip?sig=signature";
byte[] data = BingReportUtils.downloadBinaryFile(restApiHelper, azureUrl);
assertNotNull(data);
assertTrue(data.length > 0);
```

## 总结

通过这次修复，我们解决了：

✅ **URL 重复编码问题**: 正确分离 host 和 pathAndQuery  
✅ **Azure SAS 签名损坏**: 保持签名参数的完整性  
✅ **RestApiHelper 错误用法**: 使用正确的方法签名  
✅ **调试信息增强**: 添加详细的 URL 解析日志  
✅ **错误处理改进**: 更好的异常捕获和日志记录  

现在系统能够正确处理 Azure Blob Storage 的 SAS URL，成功下载压缩包文件！
